-- -------------------------------------------------------------
-- 共享停车小程序数据库 (V4 - 定稿)
-- Version: 4.0 (Final Version)
-- -------------------------------------------------------------

SET NAMES utf8mb4;

SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 用户表 (统一用户体系)
-- ----------------------------
DROP TABLE IF EXISTS `users`;

CREATE TABLE `users` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `openid` VARCHAR(128) NOT NULL COMMENT '微信用户唯一标识 OpenID',
    `nickname` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户昵称',
    `avatar_url` VARCHAR(255) NULL DEFAULT '' COMMENT '用户头像URL',
    `phone_number` VARCHAR(20) NULL DEFAULT '' COMMENT '手机号码（微信授权获取）',
    `email` VARCHAR(100) NULL DEFAULT '' COMMENT '邮箱地址',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uk_openid` (`openid`) USING BTREE,
    INDEX `idx_phone` (`phone_number`) USING BTREE,
    INDEX `idx_email` (`email`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户表（客户与员工都在此表）';

-- ----------------------------
-- 2. 停车场分类表
-- ----------------------------
DROP TABLE IF EXISTS `parking_lot_categories`;

CREATE TABLE `parking_lot_categories` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分类ID',
    `name` VARCHAR(50) NOT NULL COMMENT '分类名称',
    `icon_url` VARCHAR(255) NULL DEFAULT '' COMMENT '分类图标URL',
    `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序值，越小越靠前',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '停车场分类表';

-- ----------------------------
-- 3. 停车场信息表
-- ----------------------------
DROP TABLE IF EXISTS `parking_lots`;

CREATE TABLE `parking_lots` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '停车场ID',
    `name` VARCHAR(100) NOT NULL COMMENT '停车场名称',
    `category_id` INT UNSIGNED NULL COMMENT '所属分类ID',
    `address` VARCHAR(255) NOT NULL COMMENT '详细地址',
    `total_spaces` INT NOT NULL DEFAULT 0 COMMENT '总车位数',
    `monthly_sales` INT NOT NULL DEFAULT 0 COMMENT '月销量（订单数）',
    `rating` DECIMAL(3, 2) NOT NULL DEFAULT 0.00 COMMENT '平均评分（1.00-5.00）',
    `airport_distance_km` DECIMAL(5, 2) NULL DEFAULT NULL COMMENT '距机场距离（公里）',
    `station_distance_km` DECIMAL(5, 2) NULL DEFAULT NULL COMMENT '距高铁站距离（公里）',
    `shuttle_time_minutes` INT NULL DEFAULT NULL COMMENT '摆渡车时间（分钟）',
    `contact_phone` VARCHAR(50) NULL DEFAULT '' COMMENT '联系电话',
    `backup_phone` VARCHAR(50) NULL DEFAULT '' COMMENT '备用联系电话',
    `business_documents` JSON NULL COMMENT '营业资料（营业执照、法人身份证、场地租赁合同等）JSON格式',
    `price_rules` JSON NOT NULL COMMENT '价格规则说明 (JSON格式)',
    `service_facilities` JSON NULL COMMENT '服务设施 (JSON格式)',
    `description` TEXT NULL COMMENT '停车场详情描述',
    `image_urls` JSON NULL COMMENT '停车场图片URL列表 (JSON格式)',
    `status` ENUM(
        'approved',
        'pending_review',
        'rejected',
        'inactive'
    ) NOT NULL DEFAULT 'pending_review' COMMENT '状态',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_category_id` (`category_id`) USING BTREE,
    INDEX `idx_status` (`status`) USING BTREE,
    INDEX `idx_monthly_sales` (`monthly_sales`) USING BTREE COMMENT '月销量索引',
    INDEX `idx_rating` (`rating`) USING BTREE COMMENT '评分索引',
    FOREIGN KEY (`category_id`) REFERENCES `parking_lot_categories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '停车场信息表';

-- ----------------------------
-- 4. 停车场与管理员关联表 (核心权限表)
-- ----------------------------
DROP TABLE IF EXISTS `parking_lot_managers`;

CREATE TABLE `parking_lot_managers` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `parking_lot_id` BIGINT UNSIGNED NOT NULL COMMENT '停车场ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '被授权为管理员的用户ID',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uk_lot_manager` (`parking_lot_id`, `user_id`) USING BTREE,
    FOREIGN KEY (`parking_lot_id`) REFERENCES `parking_lots` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '停车场与管理员(员工)关联表';

-- ----------------------------
-- 5. 优惠券模板表
-- ----------------------------
DROP TABLE IF EXISTS `coupon_templates`;

CREATE TABLE `coupon_templates` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '模板ID',
    `name` VARCHAR(100) NOT NULL COMMENT '优惠券名称',
    `type` ENUM('fixed', 'discount') NOT NULL COMMENT '类型: fixed-固定金额, discount-折扣',
    `value` DECIMAL(10, 2) NOT NULL COMMENT '面值',
    `min_spend` DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最低消费金额',
    `validity_type` ENUM('fixed_days', 'date_range') NOT NULL COMMENT '有效期类型',
    `valid_days` INT NULL COMMENT '有效天数 (领取后N天内有效)',
    `valid_start_date` DATE NULL COMMENT '有效期开始日期',
    `valid_end_date` DATE NULL COMMENT '有效期结束日期',
    `total_quantity` INT NOT NULL DEFAULT -1 COMMENT '总发行量 (-1为不限量)',
    `issued_quantity` INT NOT NULL DEFAULT 0 COMMENT '已领取数量',
    `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠券模板表';

-- ----------------------------
-- 6. 用户优惠券表
-- ----------------------------
DROP TABLE IF EXISTS `user_coupons`;

CREATE TABLE `user_coupons` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户优惠券ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '所属用户ID',
    `template_id` BIGINT UNSIGNED NOT NULL COMMENT '来源模板ID',
    `order_id` BIGINT UNSIGNED NULL COMMENT '使用订单ID',
    `status` ENUM('unused', 'used', 'expired') NOT NULL DEFAULT 'unused' COMMENT '状态',
    `issue_date` DATETIME NOT NULL COMMENT '领取时间',
    `expiry_date` DATETIME NOT NULL COMMENT '过期时间',
    `used_at` DATETIME NULL COMMENT '使用时间',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uk_user_template` (`user_id`, `template_id`) USING BTREE COMMENT '确保用户对同一种券只能领一次',
    INDEX `idx_user_id` (`user_id`) USING BTREE,
    FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (`template_id`) REFERENCES `coupon_templates` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户优惠券表';

-- ----------------------------
-- 7. 订单表
-- ----------------------------
DROP TABLE IF EXISTS `orders`;

CREATE TABLE `orders` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '订单ID',
    `order_number` VARCHAR(64) NOT NULL COMMENT '订单号',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '下单用户ID',
    `parking_lot_id` BIGINT UNSIGNED NOT NULL COMMENT '停车场ID',
    `license_plate` VARCHAR(20) NOT NULL COMMENT '停车车牌号 (交易快照)',
    `planned_start_time` DATETIME NOT NULL COMMENT '预计入场时间',
    `planned_end_time` DATETIME NOT NULL COMMENT '预计离场时间',
    `actual_start_time` DATETIME NULL COMMENT '实际入场时间',
    `actual_end_time` DATETIME NULL COMMENT '实际离场时间',
    `total_amount` DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
    `discount_amount` DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额',
    `final_amount` DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT '实际支付金额',
    `status` ENUM(
        'pending_payment',
        'in_progress',
        'completed',
        'cancelled'
    ) NOT NULL DEFAULT 'pending_payment' COMMENT '订单状态',
    `payment_method` VARCHAR(50) NULL DEFAULT '' COMMENT '支付方式',
    `transaction_id` VARCHAR(128) NULL DEFAULT '' COMMENT '支付平台交易号',
    `paid_at` DATETIME NULL COMMENT '支付时间',
    `user_coupon_id` BIGINT UNSIGNED NULL COMMENT '使用的用户优惠券ID',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uk_order_number` (`order_number`) USING BTREE,
    FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (`parking_lot_id`) REFERENCES `parking_lots` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (`user_coupon_id`) REFERENCES `user_coupons` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单表';

-- ----------------------------
-- 8. 评价表
-- ----------------------------
DROP TABLE IF EXISTS `reviews`;

CREATE TABLE `reviews` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '评价ID',
    `order_id` BIGINT UNSIGNED NOT NULL COMMENT '关联订单ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '评价用户ID',
    `parking_lot_id` BIGINT UNSIGNED NOT NULL COMMENT '被评价的停车场ID',
    `rating` TINYINT UNSIGNED NOT NULL COMMENT '评分 (1-5星)',
    `comment` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文字评价内容',
    `status` ENUM('visible', 'hidden') NOT NULL DEFAULT 'visible' COMMENT '状态: visible-可见, hidden-被管理员屏蔽',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uk_order_id` (`order_id`) USING BTREE,
    FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (`parking_lot_id`) REFERENCES `parking_lots` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '评价表';

-- ----------------------------
-- 9. 评价回复表
-- ----------------------------
DROP TABLE IF EXISTS `review_replies`;

CREATE TABLE `review_replies` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '回复ID',
    `review_id` BIGINT UNSIGNED NOT NULL COMMENT '所属评价ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '回复人ID (停车场员工)',
    `content` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '回复内容',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    FOREIGN KEY (`review_id`) REFERENCES `reviews` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '评价回复表';

-- ----------------------------
-- 10. 后台管理员表 (平台超级管理员)
-- ----------------------------
DROP TABLE IF EXISTS `admins`;

CREATE TABLE `admins` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
    `username` VARCHAR(50) NOT NULL COMMENT '登录用户名',
    `password_hash` VARCHAR(255) NOT NULL COMMENT '加密后的密码',
    `is_super_admin` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为超级管理员',
    `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uk_username` (`username`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '后台管理员表';

-- ----------------------------
-- 11. 系统配置表
-- ----------------------------
DROP TABLE IF EXISTS `system_configs`;

CREATE TABLE `system_configs` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
    `config_value` TEXT NOT NULL COMMENT '配置值',
    `description` VARCHAR(255) NULL DEFAULT '' COMMENT '配置描述',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uk_config_key` (`config_key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统配置表';

-- ----------------------------
-- 触发器：自动更新停车场评分
-- ----------------------------

DELIMITER $$

-- 当有新评价时更新停车场平均评分
CREATE TRIGGER `update_parking_lot_rating_after_review_insert`
AFTER INSERT ON `reviews`
FOR EACH ROW
BEGIN
    UPDATE `parking_lots` 
    SET `rating` = (
        SELECT ROUND(AVG(rating), 2) 
        FROM `reviews` 
        WHERE `parking_lot_id` = NEW.parking_lot_id 
        AND `status` = 'visible'
    )
    WHERE `id` = NEW.parking_lot_id;
END$$

-- 当评价状态变更时更新停车场平均评分
CREATE TRIGGER `update_parking_lot_rating_after_review_update`
AFTER UPDATE ON `reviews`
FOR EACH ROW
BEGIN
    UPDATE `parking_lots` 
    SET `rating` = (
        SELECT COALESCE(ROUND(AVG(rating), 2), 0.00)
        FROM `reviews` 
        WHERE `parking_lot_id` = NEW.parking_lot_id 
        AND `status` = 'visible'
    )
    WHERE `id` = NEW.parking_lot_id;
END$$

-- 当评价被删除时更新停车场平均评分
CREATE TRIGGER `update_parking_lot_rating_after_review_delete`
AFTER DELETE ON `reviews`
FOR EACH ROW
BEGIN
    UPDATE `parking_lots` 
    SET `rating` = (
        SELECT COALESCE(ROUND(AVG(rating), 2), 0.00)
        FROM `reviews` 
        WHERE `parking_lot_id` = OLD.parking_lot_id 
        AND `status` = 'visible'
    )
    WHERE `id` = OLD.parking_lot_id;
END$$

DELIMITER;

-- ----------------------------
-- 存储过程：更新月销量
-- ----------------------------

DELIMITER $$

CREATE PROCEDURE `UpdateMonthlySales`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE lot_id BIGINT UNSIGNED;
    DECLARE sales_count INT;
    
    -- 声明游标
    DECLARE cur CURSOR FOR 
        SELECT p.id, 
               COALESCE(COUNT(o.id), 0) as monthly_orders
        FROM parking_lots p
        LEFT JOIN orders o ON p.id = o.parking_lot_id 
            AND o.created_at >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
            AND o.status IN ('completed', 'in_progress')
        GROUP BY p.id;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 打开游标
    OPEN cur;
    
    -- 循环更新
    read_loop: LOOP
        FETCH cur INTO lot_id, sales_count;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        UPDATE parking_lots 
        SET monthly_sales = sales_count 
        WHERE id = lot_id;
    END LOOP;
    
    -- 关闭游标
    CLOSE cur;
END$$

DELIMITER;

SET FOREIGN_KEY_CHECKS = 1;