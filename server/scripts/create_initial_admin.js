#!/usr/bin/env node

/**
 * 创建初始超级管理员脚本
 * 
 * 使用方法：
 * node scripts/create_initial_admin.js <用户名> <密码>
 * 
 * 示例：
 * node scripts/create_initial_admin.js admin 123456
 * 
 * 注意：
 * 1. 请确保已经创建了数据库和相关表结构
 * 2. 请确保.env文件已正确配置数据库连接信息
 * 3. 密码将自动使用bcryptjs进行哈希加密
 * 4. 创建的管理员默认为超级管理员权限
 */

const bcrypt = require('bcryptjs');
const { query, testConnection } = require('../config/db');

async function createInitialAdmin() {
  // 检查命令行参数
  const args = process.argv.slice(2);
  if (args.length !== 2) {
    console.error('❌ 使用方法: node scripts/create_initial_admin.js <用户名> <密码>');
    console.error('   示例: node scripts/create_initial_admin.js admin 123456');
    process.exit(1);
  }

  const [username, password] = args;

  // 验证输入参数
  if (!username || username.length < 3) {
    console.error('❌ 用户名不能为空且长度至少3个字符');
    process.exit(1);
  }

  if (!password || password.length < 6) {
    console.error('❌ 密码不能为空且长度至少6个字符');
    process.exit(1);
  }

  try {
    console.log('🔄 正在连接数据库...');
    
    // 测试数据库连接
    const isConnected = await testConnection();
    if (!isConnected) {
      console.error('❌ 数据库连接失败，请检查配置');
      process.exit(1);
    }

    console.log('🔄 正在检查用户名是否已存在...');
    
    // 检查用户名是否已存在
    const existingAdmin = await query(
      'SELECT id FROM admins WHERE username = ?',
      [username]
    );

    if (existingAdmin.length > 0) {
      console.error(`❌ 用户名 "${username}" 已存在，请使用其他用户名`);
      process.exit(1);
    }

    console.log('🔄 正在加密密码...');
    
    // 使用bcryptjs加密密码
    const saltRounds = 12; // 加密强度
    const passwordHash = await bcrypt.hash(password, saltRounds);

    console.log('🔄 正在创建超级管理员...');
    
    // 插入超级管理员记录
    const result = await query(
      `INSERT INTO admins (username, password_hash, is_super_admin, is_active) 
       VALUES (?, ?, 1, 1)`,
      [username, passwordHash]
    );

    if (result.affectedRows === 1) {
      console.log('✅ 超级管理员创建成功！');
      console.log(`   用户名: ${username}`);
      console.log(`   管理员ID: ${result.insertId}`);
      console.log(`   创建时间: ${new Date().toLocaleString()}`);
      console.log('');
      console.log('🔐 请妥善保管登录凭据，建议首次登录后立即修改密码');
    } else {
      console.error('❌ 创建超级管理员失败');
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ 创建过程中发生错误:', error.message);
    process.exit(1);
  } finally {
    // 退出进程
    process.exit(0);
  }
}

// 执行创建函数
createInitialAdmin();
