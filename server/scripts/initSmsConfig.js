/**
 * 初始化短信配置数据
 * 在系统配置表中添加短信相关的默认配置项
 */

const { query } = require('../config/db');

/**
 * 短信配置项
 */
const smsConfigs = [
  {
    config_key: 'sms_enabled',
    config_value: 'false',
    description: '是否启用短信功能'
  },
  {
    config_key: 'sms_provider_type',
    config_value: 'aliyun',
    description: '短信服务商类型（aliyun/tencent）'
  },
  {
    config_key: 'sms_provider_config',
    config_value: JSON.stringify({
      accessKeyId: '',
      accessKeySecret: '',
      signName: '共享停车'
    }),
    description: '短信服务商配置（JSON格式）'
  },
  {
    config_key: 'sms_order_template',
    config_value: '【共享停车】尊敬的用户，您的订单{订单号}已创建成功，车牌号：{车牌号}，停车场：{停车场名称}，停车时间：{开始时间}-{结束时间}，费用：{费用}元，请准时入场。如有疑问请联系客服。',
    description: '订单通知短信模板'
  },
  {
    config_key: 'sms_coupon_template',
    config_value: '【共享停车】恭喜您获得优惠券"{优惠券名称}"，有效期至{有效期}，快去使用吧！',
    description: '优惠券通知短信模板'
  },
  {
    config_key: 'sms_system_template',
    config_value: '【共享停车】',
    description: '系统通知短信模板前缀'
  },
  {
    config_key: 'sms_aliyun_template_code',
    config_value: 'SMS_323825475',
    description: '阿里云短信模板代码'
  }
];

/**
 * 检查配置是否已存在
 */
async function checkConfigExists(configKey) {
  try {
    const result = await query(
      'SELECT COUNT(*) as count FROM system_configs WHERE config_key = ?',
      [configKey]
    );
    return result && result[0] && result[0].count > 0;
  } catch (error) {
    console.error(`检查配置 ${configKey} 是否存在时出错:`, error);
    return false;
  }
}

/**
 * 插入配置项
 */
async function insertConfig(config) {
  try {
    await query(
      'INSERT INTO system_configs (config_key, config_value, description, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
      [config.config_key, config.config_value, config.description]
    );
    console.log(`✓ 已添加配置: ${config.config_key}`);
    return true;
  } catch (error) {
    console.error(`✗ 添加配置 ${config.config_key} 失败:`, error);
    return false;
  }
}

/**
 * 初始化短信配置
 */
async function initSmsConfig() {
  console.log('开始初始化短信配置...\n');

  let successCount = 0;
  let skipCount = 0;
  let failCount = 0;

  for (const config of smsConfigs) {
    try {
      // 检查配置是否已存在
      const exists = await checkConfigExists(config.config_key);
      
      if (exists) {
        console.log(`- 跳过已存在的配置: ${config.config_key}`);
        skipCount++;
        continue;
      }

      // 插入新配置
      const success = await insertConfig(config);
      if (success) {
        successCount++;
      } else {
        failCount++;
      }

    } catch (error) {
      console.error(`处理配置 ${config.config_key} 时出错:`, error);
      failCount++;
    }
  }

  console.log('\n短信配置初始化完成:');
  console.log(`✓ 成功添加: ${successCount} 项`);
  console.log(`- 跳过已存在: ${skipCount} 项`);
  console.log(`✗ 失败: ${failCount} 项`);

  if (failCount > 0) {
    console.log('\n请检查数据库连接和表结构是否正确');
    process.exit(1);
  } else {
    console.log('\n所有短信配置已成功初始化！');
    process.exit(0);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initSmsConfig().catch(error => {
    console.error('初始化短信配置失败:', error);
    process.exit(1);
  });
}

module.exports = {
  initSmsConfig,
  smsConfigs
};