/**
 * 初始化邮件配置脚本
 * 在系统配置表中添加邮件相关的配置项
 */

const { query } = require('../config/db');

/**
 * 邮件配置项
 */
const emailConfigs = [
  {
    config_key: 'email_enabled',
    config_value: 'false',
    description: '是否启用邮件功能'
  },
  {
    config_key: 'email_smtp_config',
    config_value: JSON.stringify({
      host: 'smtp.163.com',
      port: 465,
      user: '',
      pass: ''
    }),
    description: 'SMTP服务器配置（JSON格式）'
  },
  {
    config_key: 'email_order_template',
    config_value: `【共享停车系统】新订单通知

尊敬的停车场管理员，您好！

您管理的停车场"{停车场名称}"收到一个新的停车订单，详情如下：

订单信息：
- 订单号：{订单号}
- 车牌号：{车牌号}
- 用户昵称：{用户昵称}
- 用户手机：{用户手机}

停车时间：
- 计划入场时间：{开始时间}
- 计划出场时间：{结束时间}

费用信息：
- 停车费用：{费用}元

订单创建时间：{创建时间}

请及时关注订单状态，确保为用户提供优质的停车服务。

如有疑问，请联系系统管理员。

此邮件由系统自动发送，请勿回复。`,
    description: '订单通知邮件模板'
  },
  {
    config_key: 'email_system_template',
    config_value: `【共享停车系统】系统通知

{内容}

此邮件由系统自动发送，请勿回复。`,
    description: '系统通知邮件模板'
  }
];

/**
 * 初始化邮件配置
 */
async function initEmailConfig() {
  try {
    console.log('开始初始化邮件配置...');

    for (const config of emailConfigs) {
      // 检查配置是否已存在
      const existingConfig = await query(
        'SELECT id FROM system_configs WHERE config_key = ?',
        [config.config_key]
      );

      if (existingConfig.length > 0) {
        console.log(`配置 ${config.config_key} 已存在，跳过`);
        continue;
      }

      // 插入新配置
      await query(
        'INSERT INTO system_configs (config_key, config_value, description, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
        [config.config_key, config.config_value, config.description]
      );

      console.log(`✓ 已添加配置: ${config.config_key}`);
    }

    console.log('邮件配置初始化完成！');
    console.log('\n配置说明：');
    console.log('1. email_enabled: 控制邮件功能的开关');
    console.log('2. email_smtp_config: SMTP服务器配置，需要在管理后台设置163邮箱的用户名和授权密码');
    console.log('3. email_order_template: 订单通知邮件模板，支持变量替换');
    console.log('4. email_system_template: 系统通知邮件模板');
    console.log('\n请在管理后台的平台设置中配置具体的SMTP参数。');

  } catch (error) {
    console.error('初始化邮件配置失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initEmailConfig().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  initEmailConfig,
  emailConfigs
};
