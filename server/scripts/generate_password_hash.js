#!/usr/bin/env node

/**
 * 生成密码哈希值脚本
 * 
 * 使用方法：
 * node scripts/generate_password_hash.js <密码>
 * 
 * 示例：
 * node scripts/generate_password_hash.js parkSystem!1
 * 
 * 注意：
 * 1. 使用与系统相同的bcryptjs库和加密强度
 * 2. 加密强度为12，与系统保持一致
 * 3. 生成的哈希值可以直接用于数据库存储
 */

const bcrypt = require('bcryptjs');

async function generatePasswordHash() {
  // 检查命令行参数
  const args = process.argv.slice(2);

  // 如果没有参数，使用默认密码 parkSystem!1
  let password;
  if (args.length === 0) {
    password = 'parkSystem!1';
    console.log('ℹ️  未提供密码参数，使用默认密码: parkSystem!1');
  } else if (args.length === 1) {
    password = args[0];
  } else {
    console.error('❌ 使用方法: node scripts/generate_password_hash.js [密码]');
    console.error('   示例: node scripts/generate_password_hash.js "parkSystem!1"');
    console.error('   或者: node scripts/generate_password_hash.js (使用默认密码)');
    process.exit(1);
  }

  // 验证输入参数
  if (!password) {
    console.error('❌ 密码不能为空');
    process.exit(1);
  }

  try {
    console.log('🔄 正在生成密码哈希值...');
    console.log(`📝 原始密码: ${password}`);
    
    // 使用与系统相同的加密强度
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    console.log('✅ 密码哈希值生成成功！');
    console.log('');
    console.log('📋 结果信息:');
    console.log(`   原始密码: ${password}`);
    console.log(`   加密强度: ${saltRounds}`);
    console.log(`   哈希值: ${passwordHash}`);
    console.log('');
    console.log('🔐 请妥善保管此哈希值，可以直接用于数据库存储');
    
    // 验证哈希值是否正确
    console.log('🔄 正在验证哈希值...');
    const isValid = await bcrypt.compare(password, passwordHash);
    
    if (isValid) {
      console.log('✅ 哈希值验证成功！');
    } else {
      console.log('❌ 哈希值验证失败！');
    }

  } catch (error) {
    console.error('❌ 生成哈希值时发生错误:', error.message);
    process.exit(1);
  }
}

// 执行生成函数
generatePasswordHash();
