# 共享停车管理系统 - 后端API服务

## 项目结构

```
server/
├── config/          # 配置文件
│   └── db.js       # 数据库连接配置
├── controllers/     # 控制器（业务逻辑）
├── middleware/      # 中间件
├── models/         # 数据模型
├── routes/         # 路由定义
├── scripts/        # 工具脚本
│   └── create_initial_admin.js  # 创建初始管理员
├── utils/          # 工具函数
├── app.js          # Express应用配置
├── server.js       # 服务器启动文件
├── package.json    # 项目依赖
└── .env.example    # 环境变量示例
```

## 快速开始

### 1. 安装依赖

```bash
cd server
npm install
```

### 2. 配置环境变量

复制 `.env.example` 为 `.env` 并修改配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库连接信息：

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=parking_system

# JWT配置
JWT_SECRET=your_super_secret_jwt_key_here
```

### 3. 创建数据库

确保MySQL服务已启动，然后创建数据库：

```sql
CREATE DATABASE parking_system CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
```

导入数据库结构（在项目根目录执行）：

```bash
mysql -u root -p parking_system < sql.sql
```

### 4. 创建初始超级管理员

```bash
npm run create-admin admin 123456
```

或者直接使用node命令：

```bash
node scripts/create_initial_admin.js admin 123456
```

### 5. 启动服务器

开发模式（自动重启）：
```bash
npm run dev
```

生产模式：
```bash
npm start
```

## 可用脚本

- `npm start` - 启动生产服务器
- `npm run dev` - 启动开发服务器（nodemon）
- `npm run create-admin` - 创建初始管理员账户

## API接口

### 基础接口

- `GET /` - 服务信息
- `GET /health` - 健康检查

## 技术栈

- **Node.js** - JavaScript运行环境
- **Express.js** - Web框架
- **MySQL2** - MySQL数据库驱动
- **JWT** - 身份认证
- **bcryptjs** - 密码加密
- **CORS** - 跨域支持
- **dotenv** - 环境变量管理

## 注意事项

1. 请确保MySQL服务已启动
2. 请妥善保管JWT密钥和数据库密码
3. 生产环境请修改默认的JWT密钥
4. 建议使用PM2等进程管理器部署生产环境
