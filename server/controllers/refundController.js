/**
 * 退款相关控制器
 * 处理提前/延后离场、退款查询等功能
 */

const { query } = require('../config/db');
const refundCalculator = require('../utils/refundCalculator');
const WechatPayService = require('../services/WechatPayService');

/**
 * 处理提前离场退款
 */
const processEarlyDeparture = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { actual_end_time, reason } = req.body;
    const userId = req.user.id;

    console.log(`🚪 处理提前离场 - 订单ID: ${orderId}, 用户ID: ${userId}`);

    // 验证订单
    const orderQuery = `
      SELECT 
        id, order_number, status, final_amount, total_amount, discount_amount,
        planned_start_time, planned_end_time, actual_start_time, actual_end_time,
        parking_lot_id, transaction_id, payment_method, paid_at, user_coupon_id
      FROM orders
      WHERE id = ? AND user_id = ?
    `;

    const orders = await query(orderQuery, [orderId, userId]);

    if (orders.length === 0) {
      return res.status(404).json({
        success: false,
        message: '订单不存在或无权限访问'
      });
    }

    const order = orders[0];

    // 检查订单状态
    if (order.status !== 'in_progress') {
      return res.status(400).json({
        success: false,
        message: '只有进行中的订单才能申请提前离场退款'
      });
    }

    // 检查是否已经有实际离场时间
    if (order.actual_end_time) {
      return res.status(400).json({
        success: false,
        message: '订单已完成，无法申请提前离场退款'
      });
    }

    const actualEndTime = new Date(actual_end_time);
    const plannedEndTime = new Date(order.planned_end_time);

    // 检查是否确实是提前离场
    if (actualEndTime >= plannedEndTime) {
      return res.status(400).json({
        success: false,
        message: '实际离场时间不早于计划时间，无需申请提前离场退款'
      });
    }

    // 计算退款金额
    const refundResult = await refundCalculator.calculateEarlyDepartureRefund(order, actualEndTime);

    if (!refundResult.success) {
      return res.status(400).json({
        success: false,
        message: `退款计算失败: ${refundResult.error}`
      });
    }

    let refundInfo = null;

    // 如果有退款金额，处理退款
    if (refundResult.refundAmount > 0) {
      // 生成退款单号
      const refundNumber = refundCalculator.generateRefundNumber(order.order_number, 'ED');
      
      // 调用微信支付退款接口
      const wechatRefundResult = await WechatPayService.refund({
        transactionId: order.transaction_id,
        outRefundNo: refundNumber,
        reason: reason || refundResult.refundReason,
        refundAmount: refundResult.refundAmount,
        totalAmount: parseFloat(order.final_amount)
      });

      if (!wechatRefundResult.success) {
        return res.status(400).json({
          success: false,
          message: `退款申请失败: ${wechatRefundResult.error}`
        });
      }

      refundInfo = {
        refund_number: refundNumber,
        refund_amount: refundResult.refundAmount,
        refund_reason: refundResult.refundReason,
        refund_type: refundResult.refundType,
        wechat_refund_id: wechatRefundResult.refundId,
        refund_status: wechatRefundResult.status,
        refund_time: new Date().toISOString(),
        calculation: refundResult.calculation
      };
    }

    // 更新订单信息
    const updateFields = [
      'actual_end_time = ?',
      'status = ?',
      'updated_at = NOW()'
    ];
    const updateParams = [
      actual_end_time,
      'completed'
    ];

    // 存储退款信息
    if (refundInfo) {
      const paymentInfo = {
        original_method: order.payment_method,
        refund_info: refundInfo
      };
      updateFields.push('payment_method = ?');
      updateParams.push(JSON.stringify(paymentInfo));
    }

    updateParams.push(orderId);

    const updateQuery = `
      UPDATE orders
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;

    await query(updateQuery, updateParams);

    console.log(`✅ 提前离场处理完成 - 订单: ${order.order_number}${refundInfo ? ', 退款: ' + refundInfo.refund_amount + '元' : ', 无退款'}`);

    res.json({
      success: true,
      message: refundInfo ? 
        `提前离场处理完成，退款${refundInfo.refund_amount}元` : 
        '提前离场处理完成，无需退款',
      data: {
        order_id: orderId,
        order_number: order.order_number,
        status: 'completed',
        refund_info: refundInfo,
        calculation: refundResult
      }
    });

  } catch (error) {
    console.error('处理提前离场错误:', error);
    res.status(500).json({
      success: false,
      message: '处理提前离场失败，请稍后重试'
    });
  }
};

/**
 * 处理延迟离场补款
 */
const processLateDeparture = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { actual_end_time } = req.body;
    const userId = req.user.id;

    console.log(`⏰ 处理延迟离场 - 订单ID: ${orderId}, 用户ID: ${userId}`);

    // 验证订单
    const orderQuery = `
      SELECT 
        id, order_number, status, final_amount, total_amount, discount_amount,
        planned_start_time, planned_end_time, actual_start_time, actual_end_time,
        parking_lot_id, transaction_id, payment_method, paid_at
      FROM orders
      WHERE id = ? AND user_id = ?
    `;

    const orders = await query(orderQuery, [orderId, userId]);

    if (orders.length === 0) {
      return res.status(404).json({
        success: false,
        message: '订单不存在或无权限访问'
      });
    }

    const order = orders[0];

    // 检查订单状态
    if (order.status !== 'in_progress') {
      return res.status(400).json({
        success: false,
        message: '只有进行中的订单才能处理延迟离场'
      });
    }

    const actualEndTime = new Date(actual_end_time);
    const plannedEndTime = new Date(order.planned_end_time);

    // 检查是否确实是延迟离场
    if (actualEndTime <= plannedEndTime) {
      // 不是延迟离场，直接完成订单
      const updateQuery = `
        UPDATE orders
        SET actual_end_time = ?,
            status = 'completed',
            updated_at = NOW()
        WHERE id = ?
      `;

      await query(updateQuery, [actual_end_time, orderId]);

      return res.json({
        success: true,
        message: '订单完成，无需补款',
        data: {
          order_id: orderId,
          order_number: order.order_number,
          status: 'completed',
          additional_amount: 0
        }
      });
    }

    // 计算补款金额
    const paymentResult = await refundCalculator.calculateLateDeparturePayment(order, actualEndTime);

    if (!paymentResult.success) {
      return res.status(400).json({
        success: false,
        message: `补款计算失败: ${paymentResult.error}`
      });
    }

    // 更新订单信息
    const updateFields = [
      'actual_end_time = ?',
      'status = ?',
      'updated_at = NOW()'
    ];
    const updateParams = [
      actual_end_time,
      paymentResult.additionalAmount > 0 ? 'pending_additional_payment' : 'completed'
    ];

    // 如果需要补款，更新总金额
    if (paymentResult.additionalAmount > 0) {
      updateFields.push('total_amount = total_amount + ?');
      updateParams.push(paymentResult.additionalAmount);
    }

    updateParams.push(orderId);

    const updateQuery = `
      UPDATE orders
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;

    await query(updateQuery, updateParams);

    console.log(`✅ 延迟离场处理完成 - 订单: ${order.order_number}${paymentResult.additionalAmount > 0 ? ', 需补款: ' + paymentResult.additionalAmount + '元' : ', 无需补款'}`);

    res.json({
      success: true,
      message: paymentResult.additionalAmount > 0 ? 
        `延迟离场处理完成，需补款${paymentResult.additionalAmount}元` : 
        '延迟离场处理完成，无需补款',
      data: {
        order_id: orderId,
        order_number: order.order_number,
        status: paymentResult.additionalAmount > 0 ? 'pending_additional_payment' : 'completed',
        additional_amount: paymentResult.additionalAmount,
        calculation: paymentResult
      }
    });

  } catch (error) {
    console.error('处理延迟离场错误:', error);
    res.status(500).json({
      success: false,
      message: '处理延迟离场失败，请稍后重试'
    });
  }
};

/**
 * 查询退款状态
 */
const queryRefundStatus = async (req, res) => {
  try {
    const { orderId } = req.params;
    const userId = req.user.id;

    // 获取订单信息
    const orderQuery = `
      SELECT id, order_number, payment_method, status
      FROM orders
      WHERE id = ? AND user_id = ?
    `;

    const orders = await query(orderQuery, [orderId, userId]);

    if (orders.length === 0) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }

    const order = orders[0];
    let refundInfo = null;

    // 解析退款信息
    try {
      const paymentInfo = JSON.parse(order.payment_method);
      if (paymentInfo.refund_info) {
        refundInfo = paymentInfo.refund_info;
        
        // 查询微信支付退款状态
        if (refundInfo.refund_number) {
          const wechatResult = await WechatPayService.queryRefund(refundInfo.refund_number);
          if (wechatResult.success) {
            refundInfo.current_status = wechatResult.data?.status || refundInfo.refund_status;
          }
        }
      }
    } catch (error) {
      // payment_method不是JSON格式，可能没有退款信息
    }

    res.json({
      success: true,
      data: {
        order_id: orderId,
        order_number: order.order_number,
        order_status: order.status,
        refund_info: refundInfo
      }
    });

  } catch (error) {
    console.error('查询退款状态错误:', error);
    res.status(500).json({
      success: false,
      message: '查询失败'
    });
  }
};

module.exports = {
  processEarlyDeparture,
  processLateDeparture,
  queryRefundStatus
};
