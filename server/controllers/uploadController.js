const multer = require('multer');
const path = require('path');
const fs = require('fs');

/**
 * 图片上传控制器
 */

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../uploads/images');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置multer存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名：时间戳 + 随机数 + 原始扩展名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'parking-' + uniqueSuffix + ext);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 检查文件类型
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('只允许上传图片文件'), false);
  }
};

// 配置multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 2 * 1024 * 1024, // 2MB限制
    files: 1 // 单次只能上传一个文件
  }
});

/**
 * 单图片上传处理
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function uploadImage(req, res) {
  try {
    // 使用multer中间件处理上传
    upload.single('file')(req, res, function (err) {
      if (err instanceof multer.MulterError) {
        // Multer错误处理
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            success: false,
            message: '文件大小不能超过2MB'
          });
        } else if (err.code === 'LIMIT_FILE_COUNT') {
          return res.status(400).json({
            success: false,
            message: '一次只能上传一个文件'
          });
        } else {
          return res.status(400).json({
            success: false,
            message: '文件上传失败: ' + err.message
          });
        }
      } else if (err) {
        // 其他错误
        return res.status(400).json({
          success: false,
          message: err.message
        });
      }

      // 检查是否有文件上传
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: '请选择要上传的图片文件'
        });
      }

      // 构建文件URL
      const fileUrl = `/uploads/images/${req.file.filename}`;

      // 返回成功响应
      res.status(200).json({
        success: true,
        message: '图片上传成功',
        data: {
          url: fileUrl,
          filename: req.file.filename,
          originalName: req.file.originalname,
          size: req.file.size,
          mimetype: req.file.mimetype
        }
      });
    });

  } catch (error) {
    console.error('图片上传错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 删除图片文件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function deleteImage(req, res) {
  try {
    const { filename } = req.params;

    if (!filename) {
      return res.status(400).json({
        success: false,
        message: '请提供要删除的文件名'
      });
    }

    // 构建文件路径
    const filePath = path.join(uploadDir, filename);

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    // 删除文件
    fs.unlinkSync(filePath);

    res.status(200).json({
      success: true,
      message: '图片删除成功'
    });

  } catch (error) {
    console.error('删除图片错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

module.exports = {
  uploadImage,
  deleteImage
};
