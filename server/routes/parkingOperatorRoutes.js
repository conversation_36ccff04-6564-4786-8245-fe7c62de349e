const express = require('express');
const {
  getTodayOrdersStats,
  getTodayRevenueStats,
  getTodayParkingStats,
  getParkingOperatorOrders,
  getOrderDetail,
  updateOrderActualTime,
  getParkingLotInfo,
  getClosedDates,
  getParkingLotReviews,
  updateReviewStatus,
  replyToReview
} = require('../controllers/parkingOperatorController');
const { authenticateUser, requireParkingOperator } = require('../middleware/auth');

const router = express.Router();

// 所有停车场管理员路由都需要用户认证和停车场管理员权限
router.use(authenticateUser);
router.use(requireParkingOperator);

// ==================== 统计数据相关路由 ====================

/**
 * @route   GET /api/parking-operator/today-orders
 * @desc    获取今日订单统计数据
 * @access  Private (需要停车场管理员权限)
 * @returns { success: boolean, data: object }
 */
router.get('/today-orders', getTodayOrdersStats);

/**
 * @route   GET /api/parking-operator/today-revenue
 * @desc    获取今日业绩统计数据
 * @access  Private (需要停车场管理员权限)
 * @returns { success: boolean, data: object }
 */
router.get('/today-revenue', getTodayRevenueStats);

/**
 * @route   GET /api/parking-operator/today-parking
 * @desc    获取今日车位统计数据
 * @access  Private (需要停车场管理员权限)
 * @returns { success: boolean, data: object }
 */
router.get('/today-parking', getTodayParkingStats);

// ==================== 订单管理相关路由 ====================

/**
 * @route   GET /api/parking-operator/orders
 * @desc    获取停车场订单列表
 * @access  Private (需要停车场管理员权限)
 * @query   { page?: number, limit?: number, status?: string }
 * @returns { success: boolean, data: object }
 */
router.get('/orders', getParkingOperatorOrders);

/**
 * @route   GET /api/parking-operator/orders/:orderId
 * @desc    获取单个订单详情
 * @access  Private (需要停车场管理员权限)
 * @param   { orderId: number } 订单ID
 * @returns { success: boolean, data: object }
 */
router.get('/orders/:orderId', getOrderDetail);

/**
 * @route   PUT /api/parking-operator/orders/:orderId/actual-time
 * @desc    更新订单实际入场和出场时间
 * @access  Private (需要停车场管理员权限)
 * @param   { orderId: number } 订单ID
 * @body    { actual_start_time?: string, actual_end_time?: string, notes?: string }
 * @returns { success: boolean, data: object }
 */
router.put('/orders/:orderId/actual-time', updateOrderActualTime);

// ==================== 停车场信息相关路由 ====================

/**
 * @route   GET /api/parking-operator/parking-lot
 * @desc    获取停车场基本信息
 * @access  Private (需要停车场管理员权限)
 * @returns { success: boolean, data: object }
 */
router.get('/parking-lot', getParkingLotInfo);

/**
 * @route   GET /api/parking-operator/closed-dates
 * @desc    获取停车场关闭日期列表
 * @access  Private (需要停车场管理员权限)
 * @returns { success: boolean, data: array }
 */
router.get('/closed-dates', getClosedDates);

// ==================== 评价管理相关路由 ====================

/**
 * @route   GET /api/parking-operator/reviews
 * @desc    获取停车场评价列表
 * @access  Private (需要停车场管理员权限)
 * @query   { page?: number, pageSize?: number, status?: string }
 * @returns { success: boolean, data: { list: array, total: number, page: number, pageSize: number, totalPages: number } }
 */
router.get('/reviews', getParkingLotReviews);

/**
 * @route   PUT /api/parking-operator/reviews/:id/status
 * @desc    更新评价状态（显示/隐藏）
 * @access  Private (需要停车场管理员权限)
 * @param   { id: number } 评价ID
 * @body    { status: 'visible' | 'hidden' }
 * @returns { success: boolean, message: string }
 */
router.put('/reviews/:id/status', updateReviewStatus);

/**
 * @route   POST /api/parking-operator/reviews/:id/reply
 * @desc    回复评价
 * @access  Private (需要停车场管理员权限)
 * @param   { id: number } 评价ID
 * @body    { content: string }
 * @returns { success: boolean, message: string, data: { id: number } }
 */
router.post('/reviews/:id/reply', replyToReview);

module.exports = router;