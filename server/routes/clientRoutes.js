const express = require('express');
const {
  getBanners,
  getParkingCategories,
  getParkingLots,
  getParkingLotById,

  searchParkingLots,
  getRecommendedParkingLots,
  getPopularParkingLots,
  createOrder,
  getParkingPrice,
  getAvailableCoupons,
  validateCoupon,
  payOrder,
  cancelOrder,
  getAvailableCouponTemplates,
  claimCoupon,
  getCustomerServiceConfig,
  handlePaymentNotify,
  handleRefundNotify,
  queryPaymentStatus,
  mockPaymentSuccess
} = require('../controllers/clientController');

const {
  processEarlyDeparture,
  processLateDeparture,
  queryRefundStatus
} = require('../controllers/refundController');

const { authenticateUser } = require('../middleware/auth');

const router = express.Router();

// ==================== 内容管理路由 ====================

/**
 * @route   GET /api/content/banners
 * @desc    获取首页轮播图
 * @access  Public
 * @returns { success: boolean, data: array }
 */
router.get('/banners', getBanners);

// ==================== 停车场相关路由 ====================

/**
 * @route   GET /api/parking/categories
 * @desc    获取停车场分类列表
 * @access  Public
 * @returns { success: boolean, data: array }
 */
router.get('/categories', getParkingCategories);

/**
 * @route   GET /api/parking/lots
 * @desc    获取停车场列表
 * @access  Public
 * @query   { page?: number, limit?: number, keyword?: string, categoryId?: number, latitude?: number, longitude?: number, sortBy?: string }
 * @returns { success: boolean, data: { list: array, total: number, page: number, limit: number, totalPages: number } }
 */
router.get('/lots', getParkingLots);

/**
 * @route   GET /api/parking/lots/:id
 * @desc    获取停车场详情
 * @access  Public
 * @returns { success: boolean, data: object }
 */
router.get('/lots/:id', getParkingLotById);



/**
 * @route   GET /api/parking/search
 * @desc    搜索停车场
 * @access  Public
 * @query   { keyword: string, page?: number, limit?: number }
 * @returns { success: boolean, data: { list: array, total: number, page: number, limit: number, totalPages: number } }
 */
router.get('/search', searchParkingLots);

/**
 * @route   GET /api/parking/recommended
 * @desc    获取推荐停车场
 * @access  Public
 * @query   { limit?: number }
 * @returns { success: boolean, data: array }
 */
router.get('/recommended', getRecommendedParkingLots);

/**
 * @route   GET /api/parking/popular
 * @desc    获取热门停车场（根据订单量排序）
 * @access  Public
 * @query   { limit?: number, type?: string }
 * @returns { success: boolean, data: array }
 */
router.get('/popular', getPopularParkingLots);

// ==================== 订单相关路由 ====================

/**
 * @route   POST /api/parking/orders
 * @desc    创建停车订单
 * @access  Private (需要用户认证)
 * @body    { parking_lot_id: number, entry_time: string, exit_time: string, license_plate: string, coupon_code?: string }
 * @returns { success: boolean, data: object }
 */
router.post('/orders', authenticateUser, createOrder);

/**
 * @route   POST /api/parking/orders/:orderId/pay
 * @desc    支付订单（微信支付）
 * @access  Private (需要用户认证)
 * @params  { orderId: number }
 * @body    { openid: string }
 * @returns { success: boolean, data: object }
 */
router.post('/orders/:orderId/pay', authenticateUser, payOrder);

/**
 * @route   GET /api/parking/orders/:orderId/payment-status
 * @desc    查询支付状态
 * @access  Private (需要用户认证)
 * @params  { orderId: number }
 * @returns { success: boolean, data: object }
 */
router.get('/orders/:orderId/payment-status', authenticateUser, queryPaymentStatus);

/**
 * @route   POST /api/parking/payment/notify
 * @desc    微信支付回调通知
 * @access  Public
 * @returns { code: string, message: string }
 */
router.post('/payment/notify', handlePaymentNotify);

/**
 * @route   POST /api/parking/refund/notify
 * @desc    微信支付退款回调通知
 * @access  Public
 * @returns { code: string, message: string }
 */
router.post('/refund/notify', handleRefundNotify);

/**
 * @route   POST /api/parking/orders/:orderId/mock-pay
 * @desc    模拟支付成功（仅开发模式）
 * @access  Private (需要用户认证)
 * @params  { orderId: number }
 * @returns { success: boolean, data: object }
 */
router.post('/orders/:orderId/mock-pay', authenticateUser, mockPaymentSuccess);

/**
 * @route   POST /api/parking/orders/:orderId/cancel
 * @desc    取消订单（支持退款）
 * @access  Private (需要用户认证)
 * @params  { orderId: number }
 * @body    { reason?: string } - 取消原因（可选）
 * @returns { success: boolean, data: object }
 */
router.post('/orders/:orderId/cancel', authenticateUser, cancelOrder);

// ==================== 退改相关路由 ====================

/**
 * @route   POST /api/parking/orders/:orderId/early-departure
 * @desc    处理提前离场退款
 * @access  Private (需要用户认证)
 * @params  { orderId: number }
 * @body    { actual_end_time: string, reason?: string } - 实际离场时间和原因
 * @returns { success: boolean, data: object }
 */
router.post('/orders/:orderId/early-departure', authenticateUser, processEarlyDeparture);

/**
 * @route   POST /api/parking/orders/:orderId/late-departure
 * @desc    处理延迟离场补款
 * @access  Private (需要用户认证)
 * @params  { orderId: number }
 * @body    { actual_end_time: string } - 实际离场时间
 * @returns { success: boolean, data: object }
 */
router.post('/orders/:orderId/late-departure', authenticateUser, processLateDeparture);

/**
 * @route   GET /api/parking/orders/:orderId/refund-status
 * @desc    查询订单退款状态
 * @access  Private (需要用户认证)
 * @params  { orderId: number }
 * @returns { success: boolean, data: object }
 */
router.get('/orders/:orderId/refund-status', authenticateUser, queryRefundStatus);

/**
 * @route   GET /api/parking/price
 * @desc    获取停车价格预览
 * @access  Private (需要用户认证)
 * @query   { parking_lot_id: number, entry_time: string, exit_time: string, coupon_code?: string }
 * @returns { success: boolean, data: object }
 */
router.get('/price', authenticateUser, getParkingPrice);

// ==================== 优惠券相关路由 ====================

/**
 * @route   GET /api/parking/coupons
 * @desc    获取用户可用优惠券列表
 * @access  Private (需要用户认证)
 * @returns { success: boolean, data: array }
 */
router.get('/coupons', authenticateUser, getAvailableCoupons);

/**
 * @route   POST /api/parking/coupons/validate
 * @desc    验证优惠券
 * @access  Private (需要用户认证)
 * @body    { coupon_code: string, amount: number }
 * @returns { success: boolean, data: object }
 */
router.post('/coupons/validate', authenticateUser, validateCoupon);

// ==================== 领券中心相关路由 ====================

/**
 * @route   GET /api/coupon-center/available
 * @desc    获取可领取的优惠券模板列表
 * @access  Private (需要用户认证以显示正确的领取状态)
 * @returns { success: boolean, data: array }
 */
router.get('/available', authenticateUser, getAvailableCouponTemplates);

/**
 * @route   POST /api/coupon-center/claim
 * @desc    领取优惠券
 * @access  Private (需要用户认证)
 * @body    { template_id: number }
 * @returns { success: boolean, message: string, data: { user_coupon_id: number, coupon_name: string, expiry_date: string } }
 */
router.post('/claim', authenticateUser, claimCoupon);

// ==================== 客服配置相关路由 ====================

/**
 * @route   GET /api/content/customer-service
 * @desc    获取客服配置信息
 * @access  Public
 * @returns { success: boolean, data: { wechat: string, phone: string, qrcode: string } }
 */
router.get('/customer-service', getCustomerServiceConfig);

module.exports = router;
