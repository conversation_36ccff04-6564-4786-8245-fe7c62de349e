const express = require('express');
const {
  getUserOrders,
  getUserOrderById,
  submitOrderReview,
  getUserCoupons,
  getUserRole
} = require('../controllers/clientController');
const { authenticateUser } = require('../middleware/auth');

const router = express.Router();

// ==================== 用户订单相关路由 ====================

/**
 * @route   GET /api/user/orders
 * @desc    获取当前用户的订单列表
 * @access  Private (需要用户认证)
 * @query   { page?: number, limit?: number, status?: string }
 * @returns { success: boolean, data: { list: array, total: number, page: number, limit: number, totalPages: number } }
 */
router.get('/orders', authenticateUser, getUserOrders);

/**
 * @route   GET /api/user/orders/:id
 * @desc    获取当前用户的单个订单详情
 * @access  Private (需要用户认证)
 * @param   { id: number } 订单ID
 * @returns { success: boolean, data: object }
 */
router.get('/orders/:id', authenticateUser, getUserOrderById);

/**
 * @route   POST /api/user/orders/:id/review
 * @desc    提交订单评价
 * @access  Private (需要用户认证)
 * @param   { id: number } 订单ID
 * @body    { rating: number, comment?: string }
 * @returns { success: boolean, message: string, data: object }
 */
router.post('/orders/:id/review', authenticateUser, submitOrderReview);

// ==================== 用户优惠券相关路由 ====================

/**
 * @route   GET /api/user/coupons
 * @desc    获取当前用户的优惠券列表
 * @access  Private (需要用户认证)
 * @query   { page?: number, limit?: number, status?: string }
 * @returns { success: boolean, data: { list: array, total: number, page: number, limit: number, totalPages: number } }
 */
router.get('/coupons', authenticateUser, getUserCoupons);

// ==================== 用户角色相关路由 ====================

/**
 * @route   GET /api/user/role
 * @desc    获取当前用户的角色信息
 * @access  Private (需要用户认证)
 * @returns { success: boolean, data: object }
 */
router.get('/role', authenticateUser, getUserRole);

module.exports = router;
