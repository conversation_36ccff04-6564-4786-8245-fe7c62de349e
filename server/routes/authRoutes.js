const express = require('express');
const { getOpenid, wxLogin } = require('../controllers/clientController');

const router = express.Router();

/**
 * @route   POST /api/auth/get-openid
 * @desc    通过code获取用户openid
 * @access  Public
 * @body    { code: string }
 * @returns { success: boolean, data: { openid: string, session_key: string } }
 */
router.post('/get-openid', getOpenid);

/**
 * @route   POST /api/auth/wx-login
 * @desc    微信小程序登录
 * @access  Public
 * @body    { code: string, userInfo?: object }
 * @returns { success: boolean, data: { token: string, userInfo: object } }
 */
router.post('/wx-login', wxLogin);

module.exports = router;