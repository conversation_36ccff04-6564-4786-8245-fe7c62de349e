const express = require('express');
const {
  submitChangeRequest,
  getOperatorChangeRequests,
  getAllChangeRequests,
  processChangeRequest
} = require('../controllers/changeRequestController');

const { authenticateUser, authenticateAdmin } = require('../middleware/auth');

const router = express.Router();

// ==================== 运营商端路由 ====================

/**
 * @route   POST /api/change-requests
 * @desc    提交停车场变更申请
 * @access  Private (需要用户认证)
 * @body    { parking_lot_id: number, request_type: string, request_data: object, reason?: string }
 * @returns { success: boolean, message: string, data: object }
 */
router.post('/', authenticateUser, submitChangeRequest);

/**
 * @route   GET /api/change-requests/parking-lot/:parking_lot_id
 * @desc    获取指定停车场的申请记录列表（运营商端）
 * @access  Private (需要用户认证)
 * @params  { parking_lot_id: number }
 * @query   { page?: number, limit?: number, status?: string }
 * @returns { success: boolean, data: { list: array, total: number, page: number, limit: number, totalPages: number } }
 */
router.get('/parking-lot/:parking_lot_id', authenticateUser, getOperatorChangeRequests);

// ==================== 管理员端路由 ====================

/**
 * @route   GET /api/change-requests/admin/all
 * @desc    获取所有变更申请列表（管理员端）
 * @access  Private (需要管理员认证)
 * @query   { page?: number, limit?: number, status?: string, request_type?: string, parking_lot_name?: string }
 * @returns { success: boolean, data: { list: array, total: number, page: number, limit: number, totalPages: number } }
 */
router.get('/admin/all', authenticateAdmin, getAllChangeRequests);

/**
 * @route   POST /api/change-requests/admin/:requestId/process
 * @desc    管理员审批申请
 * @access  Private (需要管理员认证)
 * @params  { requestId: number }
 * @body    { action: 'approve' | 'reject', admin_comment?: string }
 * @returns { success: boolean, message: string, data: object }
 */
router.post('/admin/:requestId/process', authenticateAdmin, processChangeRequest);

module.exports = router;