const { query } = require('../config/db');

/**
 * 停车场价格计算引擎
 * 基于累计天数的阶梯式收费模式
 */
class ParkingPricingEngine {
  
  /**
   * 计算停车费用
   * @param {number} parkingLotId - 停车场ID
   * @param {Date|string} entryTime - 入场时间
   * @param {Date|string} exitTime - 出场时间
   * @returns {Promise<Object>} 价格计算结果
   */
  async calculatePrice(parkingLotId, entryTime, exitTime) {
    try {
      // 获取停车场价格配置
      const pricingConfig = await this.getParkingLotPricing(parkingLotId);
      
      // 计算停车天数
      const days = this.calculateParkingDays(entryTime, exitTime);
      
      // 计算累计费用
      const totalAmount = this.calculateCumulativePrice(days, pricingConfig);
      
      // 生成计算说明
      const description = this.generatePriceDescription(days, totalAmount, pricingConfig);
      
      return {
        success: true,
        parking_lot_id: parkingLotId,
        duration_days: days,
        total_amount: totalAmount,
        calculation_method: 'cumulative_daily',
        price_breakdown: {
          days: days,
          cumulative_price: totalAmount,
          description: description,
          pricing_config: pricingConfig
        }
      };
      
    } catch (error) {
      console.error('价格计算错误:', error);
      return {
        success: false,
        error: error.message,
        fallback_amount: this.calculateFallbackPrice(entryTime, exitTime)
      };
    }
  }
  
  /**
   * 计算停车天数（不足1天按1天计算）
   * @param {Date|string} entryTime - 入场时间
   * @param {Date|string} exitTime - 出场时间
   * @returns {number} 停车天数
   */
  calculateParkingDays(entryTime, exitTime) {
    const entry = new Date(entryTime);
    const exit = new Date(exitTime);
    
    if (isNaN(entry.getTime()) || isNaN(exit.getTime())) {
      throw new Error('时间格式不正确');
    }
    
    if (exit <= entry) {
      throw new Error('出场时间必须晚于入场时间');
    }
    
    // 计算时间差（毫秒）
    const timeDiff = exit.getTime() - entry.getTime();
    
    // 转换为天数
    const daysDiff = timeDiff / (1000 * 60 * 60 * 24);
    
    // 向上取整，不足1天按1天计算
    return Math.ceil(daysDiff);
  }
  
  /**
   * 获取停车场价格配置
   * @param {number} parkingLotId - 停车场ID
   * @returns {Promise<Object>} 价格配置
   */
  async getParkingLotPricing(parkingLotId) {
    try {
      const parkingLotQuery = `
        SELECT id, name, price_rules
        FROM parking_lots
        WHERE id = ? AND status = 'approved'
      `;
      
      const result = await query(parkingLotQuery, [parkingLotId]);
      
      if (result.length === 0) {
        throw new Error('停车场不存在或未审核通过');
      }
      
      const parkingLot = result[0];
      
      // 解析价格规则
      let priceRules = {};
      try {
        priceRules = typeof parkingLot.price_rules === 'string'
          ? JSON.parse(parkingLot.price_rules)
          : parkingLot.price_rules || {};
      } catch (parseError) {
        console.warn(`解析停车场 ${parkingLotId} 价格规则失败:`, parseError);
        priceRules = {};
      }
      
      // 检查是否为新的累计天数收费模式
      if (priceRules.type === 'cumulative_daily' && priceRules.daily_prices) {
        return {
          type: 'cumulative_daily',
          daily_prices: priceRules.daily_prices,
          daily_price_after_7: priceRules.daily_price_after_7 || 20.00,
          parking_lot_name: parkingLot.name
        };
      }
      
      // 如果没有配置或使用旧格式，返回默认配置
      return this.getDefaultPricingConfig(parkingLot.name);
      
    } catch (error) {
      console.error('获取停车场价格配置错误:', error);
      throw error;
    }
  }
  
  /**
   * 根据天数计算累计费用
   * @param {number} days - 停车天数
   * @param {Object} pricingConfig - 价格配置
   * @returns {number} 累计费用
   */
  calculateCumulativePrice(days, pricingConfig) {
    if (days <= 0) {
      return 0;
    }
    
    const { daily_prices, daily_price_after_7 } = pricingConfig;
    
    // 如果停车天数在1-7天内，直接返回对应的累计费用
    if (days <= 7) {
      const dayKey = `day_${days}`;
      return parseFloat(daily_prices[dayKey] || 0);
    }
    
    // 如果超过7天，计算第7天的费用加上超出天数的费用
    const day7Price = parseFloat(daily_prices.day_7 || 0);
    const extraDays = days - 7;
    const extraPrice = extraDays * parseFloat(daily_price_after_7 || 20);
    
    return day7Price + extraPrice;
  }
  
  /**
   * 生成价格计算说明
   * @param {number} days - 停车天数
   * @param {number} totalAmount - 总费用
   * @param {Object} pricingConfig - 价格配置
   * @returns {string} 计算说明
   */
  generatePriceDescription(days, totalAmount, pricingConfig) {
    if (days <= 7) {
      return `停车${days}天，累计费用${totalAmount}元`;
    }
    
    const day7Price = parseFloat(pricingConfig.daily_prices.day_7 || 0);
    const extraDays = days - 7;
    const dailyPriceAfter7 = parseFloat(pricingConfig.daily_price_after_7 || 20);
    const extraPrice = extraDays * dailyPriceAfter7;
    
    return `停车${days}天，前7天累计费用${day7Price}元，第8天起${extraDays}天每天${dailyPriceAfter7}元，总计${totalAmount}元`;
  }
  
  /**
   * 获取默认价格配置
   * @param {string} parkingLotName - 停车场名称
   * @returns {Object} 默认价格配置
   */
  getDefaultPricingConfig(parkingLotName = '停车场') {
    return {
      type: 'cumulative_daily',
      daily_prices: {
        day_1: 20.00,
        day_2: 60.00,
        day_3: 80.00,
        day_4: 100.00,
        day_5: 120.00,
        day_6: 140.00,
        day_7: 140.00
      },
      daily_price_after_7: 20.00,
      parking_lot_name: parkingLotName
    };
  }
  
  /**
   * 计算备用价格（当主要计算失败时使用）
   * @param {Date|string} entryTime - 入场时间
   * @param {Date|string} exitTime - 出场时间
   * @returns {number} 备用价格
   */
  calculateFallbackPrice(entryTime, exitTime) {
    try {
      const days = this.calculateParkingDays(entryTime, exitTime);
      const defaultConfig = this.getDefaultPricingConfig();
      return this.calculateCumulativePrice(days, defaultConfig);
    } catch (error) {
      console.error('备用价格计算也失败:', error);
      return 50; // 最后的备用价格
    }
  }
  
  /**
   * 验证价格配置的合理性
   * @param {Object} pricingConfig - 价格配置
   * @returns {Object} 验证结果
   */
  validatePricingConfig(pricingConfig) {
    const errors = [];
    
    if (!pricingConfig || typeof pricingConfig !== 'object') {
      errors.push('价格配置不能为空');
      return { valid: false, errors };
    }
    
    const { daily_prices, daily_price_after_7 } = pricingConfig;
    
    if (!daily_prices || typeof daily_prices !== 'object') {
      errors.push('每日价格配置不能为空');
      return { valid: false, errors };
    }
    
    // 检查1-7天的价格配置
    let previousPrice = 0;
    for (let day = 1; day <= 7; day++) {
      const dayKey = `day_${day}`;
      const dayPrice = parseFloat(daily_prices[dayKey]);
      
      if (isNaN(dayPrice) || dayPrice < 0) {
        errors.push(`第${day}天价格配置无效`);
      } else if (dayPrice < previousPrice) {
        errors.push(`第${day}天价格不能低于第${day-1}天价格`);
      }
      
      previousPrice = dayPrice;
    }
    
    // 检查第8天起的每日价格
    const dailyPriceAfter7Value = parseFloat(daily_price_after_7);
    if (isNaN(dailyPriceAfter7Value) || dailyPriceAfter7Value < 0) {
      errors.push('第8天起每日价格配置无效');
    }
    
    return {
      valid: errors.length === 0,
      errors: errors
    };
  }
}

// 创建单例实例
const pricingEngine = new ParkingPricingEngine();

module.exports = pricingEngine;