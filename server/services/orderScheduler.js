/**
 * 订单状态自动更新调度器
 * 定时检查并更新超时的订单状态
 */
const { query } = require('../config/db');

/**
 * 自动完成超时的订单
 * 当当前时间超过订单的预计离场时间时，自动将订单状态更新为已完成
 */
async function autoCompleteExpiredOrders() {
  try {
    console.log('🔄 开始检查超时订单...');

    // 查找需要自动完成的订单
    // 条件：
    // 1. 订单状态为 'in_progress'（进行中）
    // 2. 当前时间已超过预计离场时间
    // 3. 实际离场时间为空（还未手动完成）
    const expiredOrdersQuery = `
      SELECT 
        id, 
        order_number, 
        user_id,
        parking_lot_id,
        license_plate,
        planned_start_time,
        planned_end_time,
        final_amount
      FROM orders 
      WHERE status = 'in_progress' 
        AND planned_end_time < NOW() 
        AND actual_end_time IS NULL
    `;

    const expiredOrders = await query(expiredOrdersQuery);

    if (expiredOrders.length === 0) {
      console.log('✅ 没有需要自动完成的订单');
      return {
        success: true,
        processedCount: 0,
        message: '没有需要处理的订单'
      };
    }

    console.log(`📋 发现 ${expiredOrders.length} 个超时订单，开始处理...`);

    let successCount = 0;
    let failCount = 0;

    // 逐个处理超时订单
    for (const order of expiredOrders) {
      try {
        // 更新订单状态为已完成，并设置实际离场时间
        const updateQuery = `
          UPDATE orders 
          SET 
            status = 'completed',
            actual_end_time = NOW(),
            updated_at = NOW()
          WHERE id = ? AND status = 'in_progress'
        `;

        const updateResult = await query(updateQuery, [order.id]);

        if (updateResult.affectedRows > 0) {
          successCount++;
          console.log(`✅ 订单 ${order.order_number} 已自动完成`);
          
          // 记录日志（可选）
          await logOrderStatusChange(order, 'auto_completed');
        } else {
          failCount++;
          console.log(`❌ 订单 ${order.order_number} 更新失败`);
        }
      } catch (error) {
        failCount++;
        console.error(`❌ 处理订单 ${order.order_number} 时出错:`, error);
      }
    }

    const result = {
      success: true,
      processedCount: expiredOrders.length,
      successCount,
      failCount,
      message: `处理完成：成功 ${successCount} 个，失败 ${failCount} 个`
    };

    console.log(`🎉 订单自动完成任务执行完毕：${result.message}`);
    return result;

  } catch (error) {
    console.error('❌ 自动完成订单任务执行失败:', error);
    return {
      success: false,
      error: error.message,
      message: '任务执行失败'
    };
  }
}

/**
 * 记录订单状态变更日志
 * @param {Object} order 订单信息
 * @param {string} action 操作类型
 */
async function logOrderStatusChange(order, action) {
  try {
    // 这里可以记录到日志表或文件
    // 暂时只在控制台输出
    const logEntry = {
      timestamp: new Date().toISOString(),
      action,
      order_id: order.id,
      order_number: order.order_number,
      user_id: order.user_id,
      parking_lot_id: order.parking_lot_id,
      license_plate: order.license_plate,
      planned_end_time: order.planned_end_time,
      final_amount: order.final_amount
    };

    console.log('📝 订单状态变更日志:', JSON.stringify(logEntry, null, 2));
  } catch (error) {
    console.error('记录订单日志失败:', error);
  }
}

/**
 * 启动订单状态检查定时器
 * @param {number} intervalMinutes 检查间隔（分钟），默认5分钟
 */
function startOrderScheduler(intervalMinutes = 5) {
  const intervalMs = intervalMinutes * 60 * 1000;
  
  console.log(`🚀 启动订单状态自动检查服务，检查间隔：${intervalMinutes} 分钟`);
  
  // 立即执行一次
  autoCompleteExpiredOrders();
  
  // 设置定时器
  const timer = setInterval(() => {
    autoCompleteExpiredOrders();
  }, intervalMs);

  // 返回定时器引用，便于停止
  return timer;
}

/**
 * 停止订单状态检查定时器
 * @param {NodeJS.Timeout} timer 定时器引用
 */
function stopOrderScheduler(timer) {
  if (timer) {
    clearInterval(timer);
    console.log('⏹️ 订单状态自动检查服务已停止');
  }
}

/**
 * 手动触发订单状态检查（用于测试或手动执行）
 */
async function manualCheckOrders() {
  console.log('🔧 手动触发订单状态检查...');
  return await autoCompleteExpiredOrders();
}

module.exports = {
  autoCompleteExpiredOrders,
  startOrderScheduler,
  stopOrderScheduler,
  manualCheckOrders
};
