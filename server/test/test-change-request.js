/**
 * 变更申请功能测试脚本
 * 使用方法：node test/test-change-request.js
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000/api';
const TEST_USER_TOKEN = 'your_test_token_here'; // 需要替换为实际的用户token
const TEST_ADMIN_TOKEN = 'your_admin_token_here'; // 需要替换为实际的管理员token

// 测试数据
const testData = {
  parking_lot_id: 1, // 需要替换为实际的停车场ID
  close_dates_request: {
    request_type: 'close_dates',
    request_data: {
      closed_dates: [
        {
          date: '2024-12-25',
          reason: '圣诞节休息'
        },
        {
          date: '2024-12-26',
          reason: '节后整理'
        }
      ]
    },
    reason: '节假日期间需要关闭停车场进行维护'
  },
  modify_spaces_request: {
    request_type: 'modify_spaces',
    request_data: {
      total_spaces: 150
    },
    reason: '停车场扩建完成，增加车位数量'
  }
};

/**
 * 发送HTTP请求的辅助函数
 */
async function makeRequest(method, url, data = null, token = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: {}
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`请求失败 ${method} ${url}:`, error.response?.data || error.message);
    return null;
  }
}

/**
 * 测试提交变更申请
 */
async function testSubmitChangeRequest() {
  console.log('\n=== 测试提交变更申请 ===');
  
  // 测试关闭日期申请
  console.log('1. 测试关闭日期申请...');
  const closeDatesRequest = {
    parking_lot_id: testData.parking_lot_id,
    ...testData.close_dates_request
  };
  
  const result1 = await makeRequest('POST', '/change-requests', closeDatesRequest, TEST_USER_TOKEN);
  if (result1) {
    console.log('✅ 关闭日期申请提交成功:', result1);
  } else {
    console.log('❌ 关闭日期申请提交失败');
  }

  // 测试车位数修改申请
  console.log('2. 测试车位数修改申请...');
  const modifySpacesRequest = {
    parking_lot_id: testData.parking_lot_id,
    ...testData.modify_spaces_request
  };
  
  const result2 = await makeRequest('POST', '/change-requests', modifySpacesRequest, TEST_USER_TOKEN);
  if (result2) {
    console.log('✅ 车位数修改申请提交成功:', result2);
  } else {
    console.log('❌ 车位数修改申请提交失败');
  }
}

/**
 * 测试获取申请列表
 */
async function testGetChangeRequests() {
  console.log('\n=== 测试获取申请列表 ===');
  
  // 测试运营商端获取申请列表
  console.log('1. 测试运营商端获取申请列表...');
  const operatorResult = await makeRequest('GET', `/change-requests/parking-lot/${testData.parking_lot_id}`, null, TEST_USER_TOKEN);
  if (operatorResult) {
    console.log('✅ 运营商端申请列表获取成功:', operatorResult);
  } else {
    console.log('❌ 运营商端申请列表获取失败');
  }

  // 测试管理员端获取所有申请列表
  console.log('2. 测试管理员端获取所有申请列表...');
  const adminResult = await makeRequest('GET', '/change-requests/admin/all', null, TEST_ADMIN_TOKEN);
  if (adminResult) {
    console.log('✅ 管理员端申请列表获取成功:', adminResult);
  } else {
    console.log('❌ 管理员端申请列表获取失败');
  }
}

/**
 * 测试审批申请
 */
async function testProcessChangeRequest() {
  console.log('\n=== 测试审批申请 ===');
  
  // 首先获取待处理的申请
  const requestsList = await makeRequest('GET', '/change-requests/admin/all?status=pending', null, TEST_ADMIN_TOKEN);
  
  if (!requestsList || !requestsList.data || requestsList.data.list.length === 0) {
    console.log('❌ 没有找到待处理的申请，无法测试审批功能');
    return;
  }

  const firstRequest = requestsList.data.list[0];
  console.log(`找到待处理申请 ID: ${firstRequest.id}`);

  // 测试批准申请
  console.log('1. 测试批准申请...');
  const approveResult = await makeRequest('POST', `/change-requests/admin/${firstRequest.id}/process`, {
    action: 'approve',
    admin_comment: '申请合理，予以批准'
  }, TEST_ADMIN_TOKEN);
  
  if (approveResult) {
    console.log('✅ 申请批准成功:', approveResult);
  } else {
    console.log('❌ 申请批准失败');
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始测试变更申请功能...');
  
  // 检查配置
  if (TEST_USER_TOKEN === 'your_test_token_here' || TEST_ADMIN_TOKEN === 'your_admin_token_here') {
    console.log('❌ 请先配置测试用的token');
    console.log('1. 登录系统获取用户token和管理员token');
    console.log('2. 修改本文件中的 TEST_USER_TOKEN 和 TEST_ADMIN_TOKEN');
    return;
  }

  try {
    // 依次执行测试
    await testSubmitChangeRequest();
    await testGetChangeRequests();
    await testProcessChangeRequest();
    
    console.log('\n🎉 所有测试完成！');
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  testSubmitChangeRequest,
  testGetChangeRequests,
  testProcessChangeRequest
};