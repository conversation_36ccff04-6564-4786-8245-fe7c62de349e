const jwt = require('jsonwebtoken');
const { query } = require('../config/db');

/**
 * JWT认证中间件
 * 验证请求头中的Authorization token
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件函数
 */
async function authenticateToken(req, res, next) {
  try {
    // 从请求头获取token
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '访问令牌缺失，请先登录'
      });
    }

    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // 检查token的受众是否正确
    if (decoded.aud !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '无效的访问令牌'
      });
    }

    // 验证管理员是否仍然存在且活跃
    const adminQuery = `
      SELECT id, username, is_super_admin, is_active 
      FROM admins 
      WHERE id = ?
    `;
    
    const adminResult = await query(adminQuery, [decoded.id]);

    if (adminResult.length === 0) {
      return res.status(401).json({
        success: false,
        message: '管理员账户不存在'
      });
    }

    const admin = adminResult[0];

    if (!admin.is_active) {
      return res.status(401).json({
        success: false,
        message: '管理员账户已被禁用'
      });
    }

    // 将管理员信息添加到请求对象中
    req.admin = {
      id: admin.id,
      username: admin.username,
      is_super_admin: admin.is_super_admin
    };

    next();

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(403).json({
        success: false,
        message: '无效的访问令牌'
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '访问令牌已过期，请重新登录'
      });
    }

    console.error('JWT认证错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 超级管理员权限验证中间件
 * 必须在authenticateToken之后使用
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件函数
 */
function requireSuperAdmin(req, res, next) {
  if (!req.admin) {
    return res.status(401).json({
      success: false,
      message: '请先进行身份认证'
    });
  }

  if (!req.admin.is_super_admin) {
    return res.status(403).json({
      success: false,
      message: '需要超级管理员权限'
    });
  }

  next();
}

/**
 * 客户端用户认证中间件
 * 验证微信小程序用户的JWT token
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件函数
 */
async function authenticateUser(req, res, next) {
  try {
    // 从请求头获取token
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '访问令牌缺失，请先登录'
      });
    }

    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    console.log('decoded:', decoded);
    // 检查token类型，支持用户token和管理员token（兼容性）
    if (decoded.type === 'user') {
      // 新的用户token格式
      const userQuery = `
        SELECT id, openid, nickname, avatar_url, phone_number
        FROM users
        WHERE id = ?
      `;

      const userResult = await query(userQuery, [decoded.id]);

      if (userResult.length === 0) {
        return res.status(401).json({
          success: false,
          message: '用户账户不存在'
        });
      }

      const user = userResult[0];

      // 将用户信息添加到请求对象中
      req.user = {
        id: user.id,
        openid: user.openid,
        nickname: user.nickname,
        avatar_url: user.avatar_url,
        phone_number: user.phone_number
      };

      next();

    } else if (decoded.aud === 'admin') {
      // 兼容旧的管理员token格式（用于开发测试）
      const userQuery = `
        SELECT id, username, is_active
        FROM admins
        WHERE id = ?
      `;

      const userResult = await query(userQuery, [decoded.id]);

      if (userResult.length === 0) {
        return res.status(401).json({
          success: false,
          message: '用户账户不存在'
        });
      }

      const user = userResult[0];

      if (!user.is_active) {
        return res.status(401).json({
          success: false,
          message: '用户账户已被禁用'
        });
      }

      // 简化版本：确保在users表中有对应的用户记录
      try {
        const checkUserQuery = `SELECT id FROM users WHERE id = ?`;
        const existingUser = await query(checkUserQuery, [user.id]);

        if (existingUser.length === 0) {
          // 创建对应的用户记录
          const insertUserQuery = `
            INSERT INTO users (id, openid, nickname, created_at)
            VALUES (?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE nickname = VALUES(nickname)
          `;
          await query(insertUserQuery, [user.id, `admin_${user.id}`, user.username]);
        }
      } catch (error) {
        console.error('创建用户记录失败:', error);
      }

      // 将用户信息添加到请求对象中
      req.user = {
        id: user.id,
        username: user.username
      };

      next();

    } else {
      return res.status(403).json({
        success: false,
        message: '无效的访问令牌类型'
      });
    }

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(403).json({
        success: false,
        message: '无效的访问令牌'
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '访问令牌已过期，请重新登录'
      });
    }

    console.error('用户JWT认证错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 停车场管理员权限验证中间件
 * 验证用户是否为停车场管理员
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件函数
 */
async function requireParkingOperator(req, res, next) {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '请先进行身份认证'
      });
    }

    const { checkParkingOperatorRole } = require('../utils/roleAuth');
    const operatorRole = await checkParkingOperatorRole(req.user.id);

    if (!operatorRole) {
      return res.status(403).json({
        success: false,
        message: '需要停车场管理员权限'
      });
    }

    // 将停车场管理员信息添加到请求对象中
    req.parkingOperator = operatorRole;
    next();

  } catch (error) {
    console.error('停车场管理员权限验证错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 验证停车场访问权限中间件
 * 验证用户是否有权限访问指定的停车场
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件函数
 */
async function validateParkingAccess(req, res, next) {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '请先进行身份认证'
      });
    }

    // 从路径参数或查询参数中获取停车场ID
    const parkingLotId = req.params.parkingLotId || req.query.parking_lot_id || req.body.parking_lot_id;

    if (!parkingLotId) {
      return res.status(400).json({
        success: false,
        message: '缺少停车场ID参数'
      });
    }

    const { validateParkingLotAccess } = require('../utils/roleAuth');
    const hasAccess = await validateParkingLotAccess(req.user.id, parkingLotId);

    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: '无权限访问此停车场'
      });
    }

    next();

  } catch (error) {
    console.error('停车场访问权限验证错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 管理员认证中间件
 * 验证管理员token并设置req.admin
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件函数
 */
async function authenticateAdmin(req, res, next) {
  try {
    // 从请求头获取token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: '缺少认证token'
      });
    }

    const token = authHeader.substring(7);
    
    try {
      // 验证token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      if (decoded.type !== 'admin') {
        return res.status(401).json({
          success: false,
          message: '无效的管理员token'
        });
      }

      // 查询管理员信息
      const adminQuery = 'SELECT * FROM admins WHERE id = ? AND is_active = 1';
      const adminResult = await query(adminQuery, [decoded.id]);

      if (adminResult.length === 0) {
        return res.status(401).json({
          success: false,
          message: '管理员不存在或已被禁用'
        });
      }

      // 设置管理员信息到请求对象
      req.admin = adminResult[0];
      next();

    } catch (jwtError) {
      return res.status(401).json({
        success: false,
        message: 'token无效或已过期'
      });
    }

  } catch (error) {
    console.error('管理员认证错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

module.exports = {
  authenticateToken,
  requireSuperAdmin,
  authenticateUser,
  requireParkingOperator,
  validateParkingAccess,
  authenticateAdmin
};
