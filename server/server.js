const app = require('./app');
const { testConnection } = require('./config/db');
const { startOrderScheduler, stopOrderScheduler } = require('./services/orderScheduler');

const PORT = process.env.PORT || 3000;

// 全局变量存储定时器引用
let orderSchedulerTimer = null;

/**
 * 启动服务器
 */
async function startServer() {
  try {
    // 测试数据库连接
    console.log('🔄 正在测试数据库连接...');
    const isDbConnected = await testConnection();

    if (!isDbConnected) {
      console.error('❌ 数据库连接失败，服务器启动中止');
      process.exit(1);
    }

    // 启动HTTP服务器
    const server = app.listen(PORT, () => {
      console.log('');
      console.log('🚀 共享停车管理系统后端服务启动成功！');
      console.log(`📡 服务地址: http://localhost:${PORT}`);
      console.log(`🌍 环境模式: ${process.env.NODE_ENV || 'development'}`);
      console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);
      console.log('');
      console.log('📋 可用接口:');
      console.log(`   GET  http://localhost:${PORT}/        - 服务信息`);
      console.log(`   GET  http://localhost:${PORT}/health  - 健康检查`);
      console.log('');
    });

    // 启动订单状态自动检查服务
    console.log('🔄 启动订单状态自动检查服务...');
    orderSchedulerTimer = startOrderScheduler(5); // 每5分钟检查一次

    // 优雅关闭处理
    process.on('SIGTERM', () => {
      console.log('🔄 收到SIGTERM信号，正在优雅关闭服务器...');

      // 停止订单调度器
      if (orderSchedulerTimer) {
        stopOrderScheduler(orderSchedulerTimer);
        orderSchedulerTimer = null;
      }

      server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      console.log('🔄 收到SIGINT信号，正在优雅关闭服务器...');

      // 停止订单调度器
      if (orderSchedulerTimer) {
        stopOrderScheduler(orderSchedulerTimer);
        orderSchedulerTimer = null;
      }

      server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
      });
    });

  } catch (error) {
    console.error('❌ 服务器启动失败:', error.message);
    process.exit(1);
  }
}

// 启动服务器
startServer();
