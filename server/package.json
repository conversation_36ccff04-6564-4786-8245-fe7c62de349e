{"name": "parking-system-server", "version": "1.0.0", "description": "共享停车管理系统后端API服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "create-admin": "node scripts/create_initial_admin.js"}, "keywords": ["parking", "express", "mysql", "jwt"], "author": "", "license": "MIT", "dependencies": {"@alicloud/credentials": "^2.4.4", "@alicloud/dysmsapi20170525": "^4.1.2", "@alicloud/openapi-client": "^0.4.15", "@alicloud/tea-typescript": "^1.8.0", "@alicloud/tea-util": "^1.4.10", "@element-plus/icons-vue": "^2.3.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "element-plus": "^2.10.4", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "mysql2": "^3.6.5", "node-fetch": "^3.3.2", "nodemailer": "^7.0.5"}, "devDependencies": {"axios": "^1.10.0", "nodemon": "^3.0.2"}}