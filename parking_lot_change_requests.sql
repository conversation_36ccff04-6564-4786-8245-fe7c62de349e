-- 停车场变更申请表
DROP TABLE IF EXISTS `parking_lot_change_requests`;
CREATE TABLE `parking_lot_change_requests` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `parking_lot_id` BIGINT UNSIGNED NOT NULL COMMENT '停车场ID',
  `applicant_user_id` BIGINT UNSIGNED NOT NULL COMMENT '申请人用户ID',
  `request_type` ENUM('close_dates', 'activate','modify_spaces', 'modify_images', 'modify_info') NOT NULL COMMENT '申请类型',
  `request_data` JSON NOT NULL COMMENT '申请数据内容',
  `current_data` JSON NULL COMMENT '当前数据快照',
  `reason` TEXT NULL COMMENT '申请原因说明',
  `status` ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending' COMMENT '申请状态',
  `admin_user_id` BIGINT UNSIGNED NULL COMMENT '审批管理员ID',
  `admin_comment` TEXT NULL COMMENT '管理员审批意见',
  `processed_at` DATETIME NULL COMMENT '审批时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_parking_lot_id`(`parking_lot_id`) USING BTREE,
  INDEX `idx_applicant_user_id`(`applicant_user_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_request_type`(`request_type`) USING BTREE,
  FOREIGN KEY (`parking_lot_id`) REFERENCES `parking_lots` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY (`applicant_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '停车场变更申请表';

-- 停车场关闭日期表
DROP TABLE IF EXISTS `parking_lot_closed_dates`;
CREATE TABLE `parking_lot_closed_dates` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `parking_lot_id` BIGINT UNSIGNED NOT NULL COMMENT '停车场ID',
  `closed_date` DATE NOT NULL COMMENT '关闭日期',
  `reason` VARCHAR(255) NULL COMMENT '关闭原因',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_parking_lot_date`(`parking_lot_id`, `closed_date`) USING BTREE,
  FOREIGN KEY (`parking_lot_id`) REFERENCES `parking_lots` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '停车场关闭日期表';