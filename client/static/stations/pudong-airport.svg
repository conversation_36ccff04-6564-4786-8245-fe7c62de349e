<svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E0F6FF;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 天空背景 -->
  <rect width="300" height="200" fill="url(#skyGradient)"/>
  
  <!-- 机场建筑 -->
  <rect x="50" y="120" width="200" height="60" fill="#D3D3D3" stroke="#A9A9A9" stroke-width="2"/>
  <rect x="70" y="100" width="160" height="20" fill="#B0B0B0"/>
  
  <!-- 控制塔 -->
  <rect x="140" y="60" width="20" height="60" fill="#A9A9A9"/>
  <rect x="135" y="50" width="30" height="15" fill="#808080"/>
  
  <!-- 飞机 -->
  <g transform="translate(80,80)">
    <!-- 机身 -->
    <ellipse cx="0" cy="0" rx="40" ry="8" fill="#FFFFFF" stroke="#C0C0C0" stroke-width="1"/>
    <!-- 机翼 -->
    <ellipse cx="-10" cy="0" rx="25" ry="3" fill="#E0E0E0"/>
    <!-- 尾翼 -->
    <polygon points="-35,-2 -45,-8 -45,8 -35,2" fill="#E0E0E0"/>
  </g>
  
  <!-- 跑道 -->
  <rect x="0" y="170" width="300" height="30" fill="#696969"/>
  <rect x="0" y="180" width="300" height="2" fill="#FFFFFF"/>
  
  <!-- 文字 -->
  <text x="150" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2C3E50">上海浦东机场</text>
</svg>
