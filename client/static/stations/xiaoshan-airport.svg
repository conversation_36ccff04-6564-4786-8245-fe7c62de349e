<svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="skyGradient3" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#FFB347;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFF8DC;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 天空背景 -->
  <rect width="300" height="200" fill="url(#skyGradient3)"/>
  
  <!-- 机场建筑 -->
  <rect x="60" y="130" width="180" height="50" fill="#E6E6FA" stroke="#D8BFD8" stroke-width="2"/>
  <rect x="80" y="110" width="140" height="20" fill="#DDA0DD"/>
  
  <!-- 屋顶 -->
  <polygon points="60,130 150,90 240,130" fill="#9370DB"/>
  
  <!-- 控制塔 -->
  <rect x="130" y="70" width="15" height="40" fill="#8B7D6B"/>
  <rect x="125" y="60" width="25" height="12" fill="#A0522D"/>
  
  <!-- 飞机 -->
  <g transform="translate(120,100)">
    <!-- 机身 -->
    <ellipse cx="0" cy="0" rx="30" ry="5" fill="#FFFFFF" stroke="#C0C0C0" stroke-width="1"/>
    <!-- 机翼 -->
    <ellipse cx="-6" cy="0" rx="18" ry="2" fill="#E0E0E0"/>
    <!-- 尾翼 -->
    <polygon points="-25,-1 -32,-5 -32,5 -25,1" fill="#E0E0E0"/>
  </g>
  
  <!-- 跑道 -->
  <rect x="0" y="170" width="300" height="30" fill="#696969"/>
  <rect x="0" y="180" width="300" height="2" fill="#FFFFFF"/>
  
  <!-- 文字 -->
  <text x="150" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#8B4513">杭州萧山机场</text>
</svg>
