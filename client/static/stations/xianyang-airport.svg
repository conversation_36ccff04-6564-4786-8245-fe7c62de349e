<svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="skyGradient4" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFE5E5;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 天空背景 -->
  <rect width="300" height="200" fill="url(#skyGradient4)"/>
  
  <!-- 机场建筑 -->
  <rect x="40" y="125" width="220" height="55" fill="#F5F5DC" stroke="#D2B48C" stroke-width="2"/>
  <rect x="60" y="105" width="180" height="20" fill="#DEB887"/>
  
  <!-- 中式屋顶 -->
  <polygon points="40,125 70,95 150,85 230,95 260,125" fill="#CD853F"/>
  <polygon points="50,115 80,90 150,80 220,90 250,115" fill="#D2691E"/>
  
  <!-- 控制塔 -->
  <rect x="145" y="65" width="10" height="40" fill="#8B4513"/>
  <rect x="140" y="55" width="20" height="12" fill="#A0522D"/>
  
  <!-- 飞机 -->
  <g transform="translate(180,110)">
    <!-- 机身 -->
    <ellipse cx="0" cy="0" rx="32" ry="6" fill="#FFFFFF" stroke="#C0C0C0" stroke-width="1"/>
    <!-- 机翼 -->
    <ellipse cx="-7" cy="0" rx="19" ry="2.2" fill="#E0E0E0"/>
    <!-- 尾翼 -->
    <polygon points="-27,-1.2 -35,-5.5 -35,5.5 -27,1.2" fill="#E0E0E0"/>
  </g>
  
  <!-- 跑道 -->
  <rect x="0" y="170" width="300" height="30" fill="#696969"/>
  <rect x="0" y="180" width="300" height="2" fill="#FFFFFF"/>
  
  <!-- 文字 -->
  <text x="150" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#8B0000">西安咸阳机场</text>
</svg>
