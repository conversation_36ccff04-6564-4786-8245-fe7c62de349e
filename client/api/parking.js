/**
 * 停车场相关 API 接口
 */
import { get, post } from '../utils/request.js'

/**
 * 获取停车场列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码，默认 1
 * @param {number} params.limit 每页数量，默认 10
 * @param {string} params.keyword 搜索关键词（可选）
 * @param {number} params.categoryId 分类 ID（可选）
 * @param {string} params.sortBy 排序方式（可选：price, rating）
 * @returns {Promise} 返回停车场列表
 */
export function getParkingLots(params = {}) {
	return get('/api/parking/lots', {
		page: 1,
		limit: 10,
		...params
	})
}

/**
 * 根据 ID 获取停车场详情
 * @param {number} id 停车场 ID
 * @returns {Promise} 返回停车场详情
 */
export function getParkingLotById(id) {
	return get(`/api/parking/lots/${id}`)
}

/**
 * 获取停车场分类列表
 * @returns {Promise} 返回分类列表
 */
export function getParkingCategories() {
	return get('/api/parking/categories')
}

/**
 * 搜索停车场
 * @param {Object} params 搜索参数
 * @param {string} params.keyword 搜索关键词
 * @returns {Promise} 返回搜索结果
 */
export function searchParkingLots(params) {
	return get('/api/parking/search', params)
}



/**
 * 获取推荐停车场
 * @param {Object} params 推荐参数
 * @param {number} params.limit 返回数量，默认 10
 * @returns {Promise} 返回推荐停车场列表
 */
export function getRecommendedParkingLots(params = {}) {
	return get('/api/parking/recommended', {
		limit: 10,
		...params
	})
}

/**
 * 获取热门停车场（根据订单量排序）
 * @param {Object} params 查询参数
 * @param {number} params.limit 返回数量，默认 10
 * @param {string} params.type 类型筛选，可选值：all, airport, railway
 * @returns {Promise} 返回热门停车场列表
 */
export function getPopularParkingLots(params = {}) {
	return get('/api/parking/popular', {
		limit: 10,
		type: 'all',
		...params
	})
}

/**
 * 检查停车场可用性
 * @param {number} parkingLotId 停车场 ID
 * @param {string} startTime 开始时间
 * @param {string} endTime 结束时间
 * @returns {Promise} 返回可用性信息
 */
export function checkParkingAvailability(parkingLotId, startTime, endTime) {
	return get(`/api/parking/${parkingLotId}/availability`, {
		startTime,
		endTime
	})
}

/**
 * 创建停车订单
 * @param {Object} orderData 订单数据
 * @param {number} orderData.parking_lot_id 停车场 ID
 * @param {string} orderData.license_plate 车牌号
 * @param {string} orderData.entry_time 入场时间
 * @param {string} orderData.exit_time 离场时间
 * @param {string} orderData.coupon_code 优惠券代码（可选）
 * @returns {Promise} 返回订单创建结果
 */
export function createParkingOrder(orderData) {
	return post('/api/parking/orders', orderData)
}

/**
 * 获取停车场价格信息
 * @param {number} parkingLotId 停车场 ID
 * @param {string} entryTime 入场时间
 * @param {string} exitTime 离场时间
 * @param {string} couponCode 优惠券代码（可选）
 * @returns {Promise} 返回价格信息
 */
export function getParkingPrice(parkingLotId, entryTime, exitTime, couponCode = '') {
	return get('/api/parking/price', {
		parking_lot_id: parkingLotId,
		entry_time: entryTime,
		exit_time: exitTime,
		coupon_code: couponCode
	})
}

/**
 * 获取首页轮播图
 * @returns {Promise} 返回轮播图列表
 */
export function getBanners() {
	return get('/api/content/banners')
}

/**
 * 获取可用优惠券列表
 * @returns {Promise} 返回优惠券列表
 */
export function getAvailableCoupons() {
	return get('/api/parking/coupons')
}

/**
 * 验证优惠券
 * @param {string} couponCode 优惠券代码
 * @param {number} amount 订单金额
 * @returns {Promise} 返回验证结果
 */
export function validateCoupon(couponCode, amount) {
	return post('/api/parking/coupons/validate', {
		coupon_code: couponCode,
		amount
	})
}

/**
 * 支付订单
 * @param {number} orderId 订单ID
 * @param {string} openid 用户openid
 * @returns {Promise} 返回支付结果
 */
export function payParkingOrder(orderId, openid) {
	return post(`/api/parking/orders/${orderId}/pay`, {
		openid: openid
	})
}

/**
 * 取消订单
 * @param {number} orderId 订单ID
 * @returns {Promise} 返回取消结果
 */
export function cancelParkingOrder(orderId) {
	return post(`/api/parking/orders/${orderId}/cancel`)
}

// 默认导出
export default {
	getParkingLots,
	getParkingLotById,
	getParkingCategories,
	searchParkingLots,

	getRecommendedParkingLots,
	getPopularParkingLots,
	checkParkingAvailability,
	createParkingOrder,
	getParkingPrice,
	getBanners,
	getAvailableCoupons,
	validateCoupon,
	payParkingOrder,
	cancelParkingOrder
}
