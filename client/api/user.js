/**
 * 用户相关 API 接口
 */
import { post, get } from '../utils/request.js'

/**
 * 微信授权登录
 * @param {string} code 微信登录凭证
 * @param {Object} userInfo 用户信息（可选）
 * @returns {Promise} 返回登录结果
 */
export function wxLogin(code, userInfo = {}) {
	return post('/api/auth/wx-login', {
		code,
		userInfo
	})
}

/**
 * 获取用户信息
 * @returns {Promise} 返回用户信息
 */
export function getUserInfo() {
	return get('/api/user/info')
}

/**
 * 更新用户信息
 * @param {Object} userInfo 用户信息
 * @returns {Promise} 返回更新结果
 */
export function updateUserInfo(userInfo) {
	return post('/api/user/update', userInfo)
}

/**
 * 获取用户订单列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码，默认 1
 * @param {number} params.limit 每页数量，默认 10
 * @param {string} params.status 订单状态（可选）
 * @returns {Promise} 返回订单列表
 */
export function getUserOrders(params = {}) {
	return get('/api/user/orders', {
		page: 1,
		limit: 10,
		...params
	})
}

/**
 * 获取用户订单详情
 * @param {number} orderId 订单ID
 * @returns {Promise} 返回订单详情
 */
export function getUserOrderById(orderId) {
	return get(`/api/user/orders/${orderId}`)
}

/**
 * 获取用户优惠券列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码，默认 1
 * @param {number} params.limit 每页数量，默认 10
 * @param {string} params.status 优惠券状态（可选）
 * @returns {Promise} 返回优惠券列表
 */
export function getUserCoupons(params = {}) {
	return get('/api/user/coupons', {
		page: 1,
		limit: 10,
		...params
	})
}

/**
 * 获取用户车辆列表
 * @returns {Promise} 返回车辆列表
 */
export function getUserVehicles() {
	return get('/api/user/vehicles')
}

/**
 * 添加用户车辆
 * @param {Object} vehicle 车辆信息
 * @param {string} vehicle.plateNumber 车牌号
 * @param {string} vehicle.vehicleType 车辆类型
 * @param {string} vehicle.color 车辆颜色
 * @returns {Promise} 返回添加结果
 */
export function addUserVehicle(vehicle) {
	return post('/api/user/vehicles', vehicle)
}

/**
 * 更新用户车辆
 * @param {number} vehicleId 车辆 ID
 * @param {Object} vehicle 车辆信息
 * @returns {Promise} 返回更新结果
 */
export function updateUserVehicle(vehicleId, vehicle) {
	return post(`/api/user/vehicles/${vehicleId}`, vehicle)
}

/**
 * 删除用户车辆
 * @param {number} vehicleId 车辆 ID
 * @returns {Promise} 返回删除结果
 */
export function deleteUserVehicle(vehicleId) {
	return post(`/api/user/vehicles/${vehicleId}/delete`)
}

/**
 * 提交订单评价
 * @param {number} orderId 订单ID
 * @param {Object} reviewData 评价数据
 * @param {number} reviewData.rating 评分（1-5）
 * @param {string} reviewData.comment 评价内容（可选）
 * @returns {Promise} 返回评价结果
 */
export function submitOrderReview(orderId, reviewData) {
	return post(`/api/user/orders/${orderId}/review`, reviewData)
}

// 默认导出
export default {
	wxLogin,
	getUserInfo,
	updateUserInfo,
	getUserOrders,
	getUserOrderById,
	getUserCoupons,
	getUserVehicles,
	addUserVehicle,
	updateUserVehicle,
	deleteUserVehicle,
	submitOrderReview
}
