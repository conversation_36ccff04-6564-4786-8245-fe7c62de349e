import request from '../utils/request.js'

/**
 * 提交停车场变更申请
 * @param {Object} data - 申请数据
 * @param {number} data.parking_lot_id - 停车场ID
 * @param {string} data.request_type - 申请类型
 * @param {Object} data.request_data - 申请数据内容
 * @param {string} data.reason - 申请原因
 * @returns {Promise}
 */
export function submitChangeRequest(data) {
  return request.post('/api/change-requests', data)
}

/**
 * 获取指定停车场的申请记录列表
 * @param {number} parkingLotId - 停车场ID
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.status - 状态筛选
 * @returns {Promise}
 */
export function getOperatorChangeRequests(parkingLotId, params = {}) {
  return request.get(`/api/change-requests/parking-lot/${parkingLotId}`, { params })
}

/**
 * 提交关闭日期申请
 * @param {number} parkingLotId - 停车场ID
 * @param {Array} closedDates - 关闭日期列表
 * @param {string} reason - 申请原因
 * @returns {Promise}
 */
export function submitCloseDatesRequest(parkingLotId, closedDates, reason) {
  return submitChangeRequest({
    parking_lot_id: parkingLotId,
    request_type: 'close_dates',
    request_data: {
      closed_dates: closedDates
    },
    reason
  })
}

/**
 * 提交车位数修改申请
 * @param {number} parkingLotId - 停车场ID
 * @param {number} totalSpaces - 新的车位数
 * @param {string} reason - 申请原因
 * @returns {Promise}
 */
export function submitModifySpacesRequest(parkingLotId, totalSpaces, reason) {
  return submitChangeRequest({
    parking_lot_id: parkingLotId,
    request_type: 'modify_spaces',
    request_data: {
      total_spaces: totalSpaces
    },
    reason
  })
}

/**
 * 提交图片修改申请
 * @param {number} parkingLotId - 停车场ID
 * @param {Array} imageUrls - 新的图片URL列表
 * @param {string} reason - 申请原因
 * @returns {Promise}
 */
export function submitModifyImagesRequest(parkingLotId, imageUrls, reason) {
  return submitChangeRequest({
    parking_lot_id: parkingLotId,
    request_type: 'modify_images',
    request_data: {
      image_urls: imageUrls
    },
    reason
  })
}

/**
 * 提交基本信息修改申请
 * @param {number} parkingLotId - 停车场ID
 * @param {Object} infoData - 修改的信息数据
 * @param {string} reason - 申请原因
 * @returns {Promise}
 */
export function submitModifyInfoRequest(parkingLotId, infoData, reason) {
  return submitChangeRequest({
    parking_lot_id: parkingLotId,
    request_type: 'modify_info',
    request_data: infoData,
    reason
  })
}