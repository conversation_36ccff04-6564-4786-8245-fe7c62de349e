<script>
	import roleManager from './utils/roleManager.js'

	export default {
		globalData: {
			// API基础URL配置
			//baseUrl: 'https://shengmipark.com',  // 生产环境
			// baseUrl: 'http://*************:3000',  // 使用服务器IP
			// baseUrl: 'http://localhost:3000',  // 使用localhost
		},
		onLaunch: function() {
			console.log('App Launch')
			console.log('🌐 API Base URL:', this.globalData.baseUrl)

			// 开发环境自动注入Token（仅在开发环境生效）
			this.injectDevToken()

			// 初始化用户角色
			this.initUserRole()
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			/**
			 * 开发环境Token注入
			 * 仅在开发环境下生效，自动注入预生成的Token
			 */
			injectDevToken() {
				// 检查是否为开发环境
				let isDev = false

				// #ifdef MP-WEIXIN
				// 在微信小程序开发工具中，可以通过以下方式判断是否为开发环境
				try {
					const accountInfo = uni.getAccountInfoSync()
					isDev = accountInfo.miniProgram.envVersion === 'develop' ||
							accountInfo.miniProgram.envVersion === 'trial'
				} catch (error) {
					// 如果获取失败，默认为开发环境
					isDev = true
				}
				// #endif

				// #ifndef MP-WEIXIN
				// 在其他环境中，默认认为是开发环境
				isDev = true
				// #endif

				if (isDev) {
					console.log('🔧 开发环境检测到，正在注入Token...')

					// 预生成的开发环境Token（365天有效期）
					const devToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.VB9anXLjQZ8MuUz4lD4yM9405woFQJF7tPXoG5d_MP8'

					// 构造开发环境用户信息
					const devUserInfo = {
						id: 1,
						username: 'dev_user_1',
						nickname: '开发测试用户',
						avatar_url: '',
						phone_number: '***********',
						is_super_admin: true
					}

					try {
						// 注入Token到本地存储
						uni.setStorageSync('token', devToken)
						uni.setStorageSync('userInfo', JSON.stringify(devUserInfo))

						console.log('✅ 开发环境Token注入成功')
						console.log('🔑 Token:', devToken.substring(0, 50) + '...')
						console.log('👤 用户信息:', devUserInfo)

						// 显示提示信息
						uni.showToast({
							title: '开发环境已自动登录',
							icon: 'success',
							duration: 2000
						})

					} catch (error) {
						console.error('❌ Token注入失败:', error)
						uni.showToast({
							title: 'Token注入失败',
							icon: 'error',
							duration: 2000
						})
					}
				} else {
					console.log('📱 生产环境，跳过Token注入')
				}
			},

			/**
			 * 初始化用户角色
			 * 在应用启动时检查用户角色，但不进行页面跳转
			 */
			async initUserRole() {
				try {
					// 检查是否有Token
					const token = uni.getStorageSync('token')
					if (!token) {
						console.log('🔐 未找到Token，用户未登录')
						return
					}

					console.log('🔍 正在检查用户角色...')
					
					// 获取用户角色信息
					const roleInfo = await roleManager.getUserRole()
					
					if (roleInfo.role_type === 'parking_operator') {
						console.log('👨‍💼 检测到停车场管理员角色')
						console.log('🏢 管理停车场:', roleInfo.parking_lot_name)
					} else {
						console.log('👤 检测到普通用户角色')
					}

				} catch (error) {
					console.error('❌ 初始化用户角色失败:', error)
				}
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
</style>
