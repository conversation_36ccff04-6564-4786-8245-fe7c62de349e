import { request } from './request.js'

/**
 * 角色管理工具类
 */
class RoleManager {
  constructor() {
    this.userRole = null
    this.roleCache = null
    this.cacheExpiry = null
  }

  /**
   * 获取用户角色信息
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Object>} 用户角色信息
   */
  async getUserRole(forceRefresh = false) {
    try {
      // 检查缓存是否有效
      if (!forceRefresh && this.roleCache && this.cacheExpiry && Date.now() < this.cacheExpiry) {
        console.log('使用缓存的角色信息:', this.roleCache)
        return this.roleCache
      }

      console.log('从服务器获取角色信息...')

      // 从服务器获取角色信息
      const response = await request({
        url: '/api/user/role',
        method: 'GET'
      })

      if (response.success) {
        this.roleCache = response.data
        this.cacheExpiry = Date.now() + 5 * 60 * 1000 // 缓存5分钟
        this.userRole = response.data.role_type

        // 保存到本地存储
        uni.setStorageSync('userRole', JSON.stringify(response.data))

        console.log('角色信息获取成功:', response.data)
        return response.data
      } else {
        throw new Error(response.message || '获取用户角色失败')
      }
    } catch (error) {
      console.error('获取用户角色失败:', error)

      // 尝试从本地存储获取
      try {
        const cachedRole = uni.getStorageSync('userRole')
        if (cachedRole) {
          const roleData = JSON.parse(cachedRole)
          this.roleCache = roleData
          this.userRole = roleData.role_type
          console.log('使用本地缓存的角色信息:', roleData)
          return roleData
        }
      } catch (storageError) {
        console.error('读取本地角色缓存失败:', storageError)
      }

      // 返回默认用户角色
      const defaultRole = {
        role_type: 'user',
        permissions: ['view_own_orders', 'create_booking', 'manage_vehicles']
      }
      console.log('使用默认角色信息:', defaultRole)
      return defaultRole
    }
  }

  /**
   * 检查用户是否为停车场管理员
   * @returns {Promise<boolean>}
   */
  async isParkingOperator() {
    try {
      const roleInfo = await this.getUserRole()
      return roleInfo.role_type === 'parking_operator'
    } catch (error) {
      console.error('检查停车场管理员角色失败:', error)
      return false
    }
  }

  /**
   * 检查用户是否为普通用户
   * @returns {Promise<boolean>}
   */
  async isRegularUser() {
    try {
      const roleInfo = await this.getUserRole()
      return roleInfo.role_type === 'user'
    } catch (error) {
      console.error('检查普通用户角色失败:', error)
      return true // 默认为普通用户
    }
  }

  /**
   * 获取停车场管理员信息
   * @returns {Promise<Object|null>}
   */
  async getParkingOperatorInfo() {
    try {
      const roleInfo = await this.getUserRole()
      if (roleInfo.role_type === 'parking_operator') {
        return {
          parking_lot_id: roleInfo.parking_lot_id,
          parking_lot_name: roleInfo.parking_lot_name,
          permissions: roleInfo.permissions
        }
      }
      return null
    } catch (error) {
      console.error('获取停车场管理员信息失败:', error)
      return null
    }
  }

  /**
   * 检查用户是否有特定权限
   * @param {string} permission - 权限名称
   * @returns {Promise<boolean>}
   */
  async hasPermission(permission) {
    try {
      const roleInfo = await this.getUserRole()
      return roleInfo.permissions && roleInfo.permissions.includes(permission)
    } catch (error) {
      console.error('检查用户权限失败:', error)
      return false
    }
  }

  /**
   * 清除角色缓存
   */
  clearCache() {
    this.roleCache = null
    this.cacheExpiry = null
    this.userRole = null
    
    try {
      uni.removeStorageSync('userRole')
    } catch (error) {
      console.error('清除角色缓存失败:', error)
    }
  }

  /**
   * 刷新用户角色
   * @returns {Promise<Object>}
   */
  async refreshUserRole() {
    return await this.getUserRole(true)
  }

  /**
   * 初始化角色信息（优先使用本地缓存）
   * @returns {Promise<Object>}
   */
  async initUserRole() {
    try {
      // 先尝试从本地存储获取
      const cachedRole = uni.getStorageSync('userRole')
      if (cachedRole) {
        const roleData = JSON.parse(cachedRole)
        this.roleCache = roleData
        this.userRole = roleData.role_type
        console.log('初始化：使用本地缓存的角色信息:', roleData)

        // 异步更新角色信息，但不等待结果
        this.getUserRole(true).catch(error => {
          console.error('后台更新角色信息失败:', error)
        })

        return roleData
      }

      // 如果没有缓存，则从服务器获取
      return await this.getUserRole()
    } catch (error) {
      console.error('初始化角色信息失败:', error)
      return {
        role_type: 'user',
        permissions: ['view_own_orders', 'create_booking', 'manage_vehicles']
      }
    }
  }

  /**
   * 根据用户角色跳转到相应的主页
   */
  async navigateToHomePage() {
    try {
      const isParkingOp = await this.isParkingOperator()
      
      if (isParkingOp) {
        // 跳转到停车场管理员主页
        uni.reLaunch({
          url: '/pages/parking-operator/dashboard'
        })
      } else {
        // 跳转到普通用户主页
        uni.reLaunch({
          url: '/pages/index/index'
        })
      }
    } catch (error) {
      console.error('角色导航失败:', error)
      // 默认跳转到普通用户主页
      uni.reLaunch({
        url: '/pages/index/index'
      })
    }
  }

  /**
   * 检查页面访问权限
   * @param {string} pagePath - 页面路径
   * @returns {Promise<boolean>}
   */
  async checkPageAccess(pagePath) {
    try {
      const roleInfo = await this.getUserRole()
      
      // 停车场管理员专用页面
      const operatorPages = [
        '/pages/parking-operator/dashboard',
        '/pages/parking-operator/orders'
      ]
      
      if (operatorPages.some(page => pagePath.includes(page))) {
        return roleInfo.role_type === 'parking_operator'
      }
      
      // 普通用户页面，所有角色都可以访问
      return true
    } catch (error) {
      console.error('检查页面访问权限失败:', error)
      return true // 默认允许访问
    }
  }
}

// 创建单例实例
const roleManager = new RoleManager()

export default roleManager