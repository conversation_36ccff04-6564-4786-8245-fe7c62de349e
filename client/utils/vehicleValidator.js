/**
 * 车辆数据验证工具函数
 * 提供车牌号格式验证和数据完整性检查
 */

// 车辆类型枚举
const VEHICLE_TYPES = {
  small: '小型车',
  medium: '中型车',
  large: '大型车',
  suv: 'SUV',
  truck: '货车'
}

// 车辆颜色选项
const VEHICLE_COLORS = [
  '白色', '黑色', '银色', '灰色', '红色',
  '蓝色', '绿色', '黄色', '棕色', '其他'
]

// 车牌号验证正则表达式
const LICENSE_PLATE_REGEX = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/

/**
 * 验证车牌号格式
 * @param {string} plateNumber 车牌号
 * @returns {Object} 验证结果
 */
function validatePlateNumber(plateNumber) {
  if (!plateNumber || typeof plateNumber !== 'string') {
    return {
      valid: false,
      message: '车牌号不能为空'
    }
  }
  
  // 去除空格并转换为大写
  const cleanPlateNumber = plateNumber.replace(/\s/g, '').toUpperCase()
  
  if (cleanPlateNumber.length < 7 || cleanPlateNumber.length > 8) {
    return {
      valid: false,
      message: '车牌号长度不正确'
    }
  }
  
  if (!LICENSE_PLATE_REGEX.test(cleanPlateNumber)) {
    return {
      valid: false,
      message: '车牌号格式不正确，请输入正确的车牌号'
    }
  }
  
  return {
    valid: true,
    message: '车牌号格式正确',
    cleanValue: cleanPlateNumber
  }
}

/**
 * 验证车辆类型
 * @param {string} vehicleType 车辆类型
 * @returns {Object} 验证结果
 */
function validateVehicleType(vehicleType) {
  if (!vehicleType) {
    return {
      valid: false,
      message: '请选择车辆类型'
    }
  }
  
  if (!VEHICLE_TYPES.hasOwnProperty(vehicleType)) {
    return {
      valid: false,
      message: '车辆类型不正确'
    }
  }
  
  return {
    valid: true,
    message: '车辆类型正确'
  }
}

/**
 * 验证车辆品牌
 * @param {string} brand 车辆品牌
 * @returns {Object} 验证结果
 */
function validateBrand(brand) {
  if (!brand || typeof brand !== 'string') {
    return {
      valid: true, // 品牌不是必填项
      message: '品牌为空'
    }
  }
  
  if (brand.length > 50) {
    return {
      valid: false,
      message: '品牌名称过长，请控制在50字符以内'
    }
  }
  
  return {
    valid: true,
    message: '品牌格式正确'
  }
}

/**
 * 验证车辆型号
 * @param {string} model 车辆型号
 * @returns {Object} 验证结果
 */
function validateModel(model) {
  if (!model || typeof model !== 'string') {
    return {
      valid: true, // 型号不是必填项
      message: '型号为空'
    }
  }
  
  if (model.length > 50) {
    return {
      valid: false,
      message: '型号名称过长，请控制在50字符以内'
    }
  }
  
  return {
    valid: true,
    message: '型号格式正确'
  }
}

/**
 * 验证车辆颜色
 * @param {string} color 车辆颜色
 * @returns {Object} 验证结果
 */
function validateColor(color) {
  if (!color || typeof color !== 'string') {
    return {
      valid: true, // 颜色不是必填项
      message: '颜色为空'
    }
  }
  
  if (color.length > 20) {
    return {
      valid: false,
      message: '颜色名称过长，请控制在20字符以内'
    }
  }
  
  return {
    valid: true,
    message: '颜色格式正确'
  }
}

/**
 * 验证车辆数据完整性
 * @param {Object} vehicleData 车辆数据
 * @returns {Object} 验证结果
 */
function validateVehicleData(vehicleData) {
  const errors = []
  const warnings = []
  
  if (!vehicleData || typeof vehicleData !== 'object') {
    return {
      valid: false,
      message: '车辆数据格式错误',
      errors: ['车辆数据不能为空']
    }
  }
  
  // 验证车牌号（必填）
  const plateResult = validatePlateNumber(vehicleData.plate_number)
  if (!plateResult.valid) {
    errors.push(plateResult.message)
  }
  
  // 验证车辆类型（必填）
  const typeResult = validateVehicleType(vehicleData.vehicle_type)
  if (!typeResult.valid) {
    errors.push(typeResult.message)
  }
  
  // 验证品牌（可选）
  const brandResult = validateBrand(vehicleData.brand)
  if (!brandResult.valid) {
    errors.push(brandResult.message)
  } else if (!vehicleData.brand) {
    warnings.push('建议填写车辆品牌')
  }
  
  // 验证型号（可选）
  const modelResult = validateModel(vehicleData.model)
  if (!modelResult.valid) {
    errors.push(modelResult.message)
  } else if (!vehicleData.model) {
    warnings.push('建议填写车辆型号')
  }
  
  // 验证颜色（可选）
  const colorResult = validateColor(vehicleData.color)
  if (!colorResult.valid) {
    errors.push(colorResult.message)
  } else if (!vehicleData.color) {
    warnings.push('建议填写车辆颜色')
  }
  
  return {
    valid: errors.length === 0,
    message: errors.length === 0 ? '车辆数据验证通过' : '车辆数据验证失败',
    errors,
    warnings,
    cleanData: errors.length === 0 ? {
      plate_number: plateResult.cleanValue || vehicleData.plate_number,
      vehicle_type: vehicleData.vehicle_type,
      brand: vehicleData.brand || '',
      model: vehicleData.model || '',
      color: vehicleData.color || ''
    } : null
  }
}

/**
 * 格式化车牌号
 * @param {string} plateNumber 原始车牌号
 * @returns {string} 格式化后的车牌号
 */
function formatPlateNumber(plateNumber) {
  if (!plateNumber || typeof plateNumber !== 'string') {
    return ''
  }
  
  return plateNumber.replace(/\s/g, '').toUpperCase()
}

/**
 * 获取车辆类型显示名称
 * @param {string} vehicleType 车辆类型代码
 * @returns {string} 显示名称
 */
function getVehicleTypeName(vehicleType) {
  return VEHICLE_TYPES[vehicleType] || '未知类型'
}

/**
 * 检查车辆数据是否完整
 * @param {Object} vehicleData 车辆数据
 * @returns {Object} 完整性检查结果
 */
function checkDataCompleteness(vehicleData) {
  if (!vehicleData) {
    return {
      complete: false,
      completeness: 0,
      missingFields: ['所有字段']
    }
  }
  
  const requiredFields = ['plate_number', 'vehicle_type']
  const optionalFields = ['brand', 'model', 'color']
  const allFields = [...requiredFields, ...optionalFields]
  
  const missingRequired = requiredFields.filter(field => !vehicleData[field])
  const missingOptional = optionalFields.filter(field => !vehicleData[field])
  
  const filledFields = allFields.filter(field => vehicleData[field])
  const completeness = Math.round((filledFields.length / allFields.length) * 100)
  
  return {
    complete: missingRequired.length === 0,
    completeness,
    missingRequired,
    missingOptional,
    suggestions: missingOptional.length > 0 ? 
      `建议补充：${missingOptional.map(field => {
        switch(field) {
          case 'brand': return '品牌'
          case 'model': return '型号'
          case 'color': return '颜色'
          default: return field
        }
      }).join('、')}` : '信息已完整'
  }
}

/**
 * 生成车辆显示名称
 * @param {Object} vehicleData 车辆数据
 * @returns {string} 显示名称
 */
function generateVehicleDisplayName(vehicleData) {
  if (!vehicleData || !vehicleData.plate_number) {
    return '未知车辆'
  }
  
  const parts = [vehicleData.plate_number]
  
  if (vehicleData.brand) {
    parts.push(vehicleData.brand)
  }
  
  if (vehicleData.model) {
    parts.push(vehicleData.model)
  }
  
  if (vehicleData.color) {
    parts.push(vehicleData.color)
  }
  
  return parts.join(' ')
}

// 导出所有函数和常量
export {
  VEHICLE_TYPES,
  VEHICLE_COLORS,
  LICENSE_PLATE_REGEX,
  validatePlateNumber,
  validateVehicleType,
  validateBrand,
  validateModel,
  validateColor,
  validateVehicleData,
  formatPlateNumber,
  getVehicleTypeName,
  checkDataCompleteness,
  generateVehicleDisplayName
}

// 默认导出
export default {
  VEHICLE_TYPES,
  VEHICLE_COLORS,
  LICENSE_PLATE_REGEX,
  validatePlateNumber,
  validateVehicleType,
  validateBrand,
  validateModel,
  validateColor,
  validateVehicleData,
  formatPlateNumber,
  getVehicleTypeName,
  checkDataCompleteness,
  generateVehicleDisplayName
}