import roleManager from './roleManager.js'

/**
 * 页面权限守卫混入
 * 用于在页面加载时检查用户权限
 */
export const roleGuardMixin = {
  async onLoad() {
    // 检查页面访问权限
    await this.checkPagePermission()
  },

  methods: {
    /**
     * 检查页面权限
     */
    async checkPagePermission() {
      try {
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1]
        const pagePath = currentPage.route

        // 检查是否有访问权限
        const hasAccess = await roleManager.checkPageAccess(pagePath)
        
        if (!hasAccess) {
          console.warn('⚠️ 用户无权限访问页面:', pagePath)
          
          uni.showModal({
            title: '访问受限',
            content: '您没有权限访问此页面',
            showCancel: false,
            success: () => {
              // 跳转到相应的主页
              roleManager.navigateToHomePage()
            }
          })
          return false
        }

        return true
      } catch (error) {
        console.error('检查页面权限失败:', error)
        return true // 默认允许访问
      }
    }
  }
}

/**
 * 停车场管理员页面守卫
 * 专门用于停车场管理员页面的权限检查
 */
export const parkingOperatorGuard = {
  async onLoad() {
    await this.checkParkingOperatorPermission()
  },

  methods: {
    /**
     * 检查停车场管理员权限
     */
    async checkParkingOperatorPermission() {
      try {
        const isParkingOp = await roleManager.isParkingOperator()
        
        if (!isParkingOp) {
          console.warn('⚠️ 非停车场管理员尝试访问管理页面')
          
          uni.showModal({
            title: '权限不足',
            content: '此页面仅限停车场管理员访问',
            showCancel: false,
            success: () => {
              uni.reLaunch({
                url: '/pages/index/index'
              })
            }
          })
          return false
        }

        // 获取停车场信息并设置到页面数据中
        const operatorInfo = await roleManager.getParkingOperatorInfo()
        if (operatorInfo && this.setParkingOperatorInfo) {
          this.setParkingOperatorInfo(operatorInfo)
        }

        return true
      } catch (error) {
        console.error('检查停车场管理员权限失败:', error)
        
        uni.showModal({
          title: '权限验证失败',
          content: '无法验证您的权限，请重新登录',
          showCancel: false,
          success: () => {
            uni.reLaunch({
              url: '/pages/index/index'
            })
          }
        })
        return false
      }
    }
  }
}

/**
 * 角色导航工具函数
 */
export const roleNavigation = {
  /**
   * 智能导航到主页
   * 根据用户角色自动跳转到相应的主页
   */
  async navigateToHome() {
    try {
      const isParkingOp = await roleManager.isParkingOperator()
      
      if (isParkingOp) {
        uni.switchTab({
          url: '/pages/parking-operator/dashboard'
        })
      } else {
        uni.switchTab({
          url: '/pages/index/index'
        })
      }
    } catch (error) {
      console.error('智能导航失败:', error)
      uni.switchTab({
        url: '/pages/index/index'
      })
    }
  },

  /**
   * 检查并跳转到角色对应的主页
   * 如果当前页面不适合用户角色，则自动跳转
   */
  async checkAndRedirect() {
    try {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const pagePath = currentPage.route

      const isParkingOp = await roleManager.isParkingOperator()
      
      // 如果是停车场管理员，但在普通用户主页
      if (isParkingOp && pagePath === 'pages/index/index') {
        uni.reLaunch({
          url: '/pages/parking-operator/dashboard'
        })
        return
      }
      
      // 如果是普通用户，但在管理员页面
      if (!isParkingOp && pagePath.includes('parking-operator')) {
        uni.reLaunch({
          url: '/pages/index/index'
        })
        return
      }
    } catch (error) {
      console.error('角色检查和重定向失败:', error)
    }
  }
}