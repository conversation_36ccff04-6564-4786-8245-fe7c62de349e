/**
 * 统一的网络请求封装
 * 基于 uni.request 封装，提供统一的错误处理、token 管理等功能
 */

// 基础配置
// 开发环境配置
const DEV_BASE_URL = 'http://localhost:3000'
// 生产环境配置（正式域名，SSL证书正常）
const PROD_BASE_URL = 'https://api.shengmipark.com'

// 使用正式域名（服务器配置正常，SSL证书有效）
const BASE_URL = DEV_BASE_URL

console.log('当前API地址:', BASE_URL)
const TIMEOUT = 1000000 // 请求超时时间

/**
 * 处理图片URL，将相对路径转换为完整URL
 * @param {string} imageUrl 图片URL
 * @returns {string} 完整的图片URL
 */
export function getFullImageUrl(imageUrl) {
	if (!imageUrl) {
		return '/static/parking-default.svg'
	}

	// 如果是相对路径，转换为完整URL
	if (imageUrl.startsWith('/uploads/')) {
		return `${BASE_URL}${imageUrl}`
	}

	// 如果已经是完整URL或本地路径，直接返回
	return imageUrl
}

/**
 * 统一请求函数
 * @param {Object} options 请求配置
 * @param {string} options.url 请求地址
 * @param {string} options.method 请求方法，默认 GET
 * @param {Object} options.data 请求数据
 * @param {Object} options.header 请求头
 * @param {boolean} options.loading 是否显示加载提示，默认 true
 * @returns {Promise} 返回 Promise 对象
 */
export function request(options = {}) {
	return new Promise((resolve, reject) => {
		// 显示加载提示
		if (options.loading !== false) {
			uni.showLoading({
				title: '加载中...',
				mask: true
			})
		}

		// 获取本地存储的 token
		let token = null;
		try {
			const userInfoStr = uni.getStorageSync('userInfo');
			if (userInfoStr) {
				const userInfo = JSON.parse(userInfoStr);
				token = userInfo.token;
			}
		} catch (error) {
			console.warn('解析用户信息失败:', error);
			// 清除可能损坏的数据
			uni.removeStorageSync('userInfo');
		}

		// 开发环境：如果没有token且不跳过默认token，使用开发用的假token
		if (!token && !options.skipDefaultToken) {
			// 这是一个开发用的JWT token，有效期365天
			// 在生产环境中应该通过正常的登录流程获取token
			token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwidXNlcm5hbWUiOiJhZG1pbiIsImF1ZCI6ImFkbWluIiwiaWF0IjoxNzUyMzczNDcwLCJleHAiOjE3ODM5MDk0NzB9.XwB-jnSQ4GLTDvg4v6bO_thuxdPyQP4jczJHkwKbxas'
			console.log('🔧 开发模式：使用开发token进行测试')
		}

		// 构建请求头
		const header = {
			'Content-Type': 'application/json',
			...options.header
		}

		// 如果有 token，添加到请求头
		if (token) {
			header.Authorization = `Bearer ${token}`
		}

		// 发起请求
		uni.request({
			url: BASE_URL + options.url,
			method: options.method || 'GET',
			data: options.data || {},
			header,
			timeout: TIMEOUT,
			success: (res) => {
				// 隐藏加载提示
				if (options.loading !== false) {
					uni.hideLoading()
				}

				// 处理 HTTP 状态码
				if (res.statusCode === 200 || res.statusCode === 201) {
					const data = res.data

					// 处理业务状态码
					if (data.success) {
						resolve(data)
					} else {
						// 业务错误
						const errorMsg = data.message || '请求失败'
						uni.showToast({
							title: errorMsg,
							icon: 'none',
							duration: 2000
						})
						reject(new Error(errorMsg))
					}
				} else if (res.statusCode === 401) {
					// token 过期或无效，清除本地存储并跳转到登录页
					uni.removeStorageSync('token')
					uni.removeStorageSync('userInfo')
					uni.showToast({
						title: '登录已过期，请重新登录',
						icon: 'none',
						duration: 2000
					})
					// 跳转到我的页面（包含登录功能）
					uni.switchTab({
						url: '/pages/my/my'
					})
					reject(new Error('登录已过期'))
				} else {
					// 其他 HTTP 错误
					const errorMsg = `请求失败 (${res.statusCode})`
					uni.showToast({
						title: errorMsg,
						icon: 'none',
						duration: 2000
					})
					reject(new Error(errorMsg))
				}
			},
			fail: (err) => {
				// 隐藏加载提示
				if (options.loading !== false) {
					uni.hideLoading()
				}

				// 详细的网络错误日志
				console.error('网络请求失败详情:', {
					url: BASE_URL + options.url,
					method: options.method || 'GET',
					error: err,
					errMsg: err.errMsg,
					statusCode: err.statusCode
				})

				// 网络错误
				let errorMsg = '网络连接失败'
				if (err.errMsg) {
					if (err.errMsg.includes('timeout')) {
						errorMsg = '请求超时，请检查网络连接'
					} else if (err.errMsg.includes('fail')) {
						errorMsg = '网络连接失败，请检查网络设置'
					} else if (err.errMsg.includes('ssl')) {
						errorMsg = 'SSL证书验证失败'
					} else if (err.errMsg.includes('domain')) {
						errorMsg = '域名不在合法域名列表中'
					}
				}

				uni.showToast({
					title: errorMsg,
					icon: 'none',
					duration: 3000
				})
				reject(new Error(errorMsg))
			}
		})
	})
}

/**
 * GET 请求
 * @param {string} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 其他配置
 */
export function get(url, data = {}, options = {}) {
	return request({
		url,
		method: 'GET',
		data,
		...options
	})
}

/**
 * POST 请求
 * @param {string} url 请求地址
 * @param {Object} data 请求数据
 * @param {Object} options 其他配置
 */
export function post(url, data = {}, options = {}) {
	// 对于登录接口，不使用假token
	if (url === '/api/auth/wx-login') {
		options.skipDefaultToken = true
	}
	
	return request({
		url,
		method: 'POST',
		data,
		...options
	})
}

/**
 * PUT 请求
 * @param {string} url 请求地址
 * @param {Object} data 请求数据
 * @param {Object} options 其他配置
 */
export function put(url, data = {}, options = {}) {
	return request({
		url,
		method: 'PUT',
		data,
		...options
	})
}

/**
 * DELETE 请求
 * @param {string} url 请求地址
 * @param {Object} data 请求数据
 * @param {Object} options 其他配置
 */
export function del(url, data = {}, options = {}) {
	return request({
		url,
		method: 'DELETE',
		data,
		...options
	})
}

// 默认导出
export default {
	request,
	get,
	post,
	put,
	del
}
