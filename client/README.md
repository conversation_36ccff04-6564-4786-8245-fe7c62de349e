# 共享停车小程序

基于 Uni-app + Vue 3 + TypeScript 开发的共享停车小程序前端代码。

## 技术栈

- **框架：** Uni-app
- **语言：** Vue 3 (Composition API) + TypeScript
- **构建工具：** HBuilderX 或 CLI
- **页面生命周期：** 使用 `@dcloudio/uni-app` 提供的组合式 API

## 项目结构

```
client/
├── pages/                  # 页面文件
│   ├── index/              # 首页
│   │   └── index.vue
│   ├── list/               # 找车位页面
│   │   └── list.vue
│   ├── order/              # 订单页面
│   │   └── order.vue
│   ├── my/                 # 我的页面
│   │   └── my.vue
│   └── detail/             # 停车场详情页
│       └── detail.vue
├── api/                    # API 接口封装
│   ├── user.js             # 用户相关接口
│   └── parking.js          # 停车场相关接口
├── utils/                  # 工具函数
│   └── request.js          # 网络请求封装
├── static/                 # 静态资源
│   ├── logo.png
│   └── README.md           # 静态资源说明
├── pages.json              # 页面配置
├── App.vue                 # 应用入口
├── main.js                 # 主入口文件
├── manifest.json           # 应用配置
└── uni.scss               # 全局样式
```

## 已实现功能

### 1. 基础框架
- ✅ 四个主页面的底部导航栏配置
- ✅ 页面路由配置
- ✅ 统一的网络请求封装
- ✅ API 接口模块化管理

### 2. 我的页面
- ✅ 微信授权登录功能
- ✅ 登录状态管理
- ✅ 用户信息显示
- ✅ 功能菜单导航
- ✅ 退出登录功能

### 3. 首页
- ✅ 搜索框（点击跳转搜索页）
- ✅ 轮播图广告展示
- ✅ 停车场分类入口
- ✅ 附近/推荐停车场列表
- ✅ 下拉刷新功能
- ✅ 点击跳转详情页

### 4. 停车场详情页
- ✅ 图片轮播展示
- ✅ 基本信息显示
- ✅ 收费标准说明
- ✅ 服务设施展示
- ✅ 地图定位功能
- ✅ 一键导航功能
- ✅ 立即预定功能

## 接口说明

### 后端接口地址
默认配置：`http://localhost:3000`

### 主要接口
- `POST /api/admin/login` - 微信登录（临时使用管理员接口）
- `GET /api/admin/parking-lots` - 获取停车场列表
- `GET /api/admin/parking-lots/:id` - 获取停车场详情
- `GET /api/content/banners` - 获取轮播图
- `GET /api/parking/nearby` - 获取附近停车场

## 开发说明

### 1. 环境要求
- HBuilderX 3.0+ 或 @dcloudio/cli
- Node.js 14+
- 微信开发者工具

### 2. 运行项目
```bash
# 使用 HBuilderX
# 直接打开项目文件夹，点击运行到微信小程序

# 使用 CLI（如果配置了）
npm run dev:mp-weixin
```

### 3. 配置说明
- 修改 `utils/request.js` 中的 `BASE_URL` 为实际后端地址
- 在微信开发者工具中配置小程序 AppID
- 添加必要的静态资源图片（参考 `static/README.md`）

### 4. 待完善功能
- 找车位页面的筛选和搜索功能
- 订单页面的订单管理功能
- 预定页面的创建
- 搜索页面的创建
- 车辆管理页面
- 优惠券页面
- 客服页面等

## 注意事项

1. **登录功能：** 当前使用管理员登录接口模拟，实际部署时需要替换为用户登录接口
2. **图片资源：** 需要添加相应的图标和图片资源
3. **权限配置：** 需要在微信小程序后台配置相应的接口域名
4. **地图功能：** 需要在微信小程序后台开启位置权限

## 下一步开发建议

1. 完善找车位页面的搜索和筛选功能
2. 实现订单管理相关页面
3. 添加支付功能集成
4. 完善用户车辆管理
5. 添加优惠券功能
6. 集成客服功能
