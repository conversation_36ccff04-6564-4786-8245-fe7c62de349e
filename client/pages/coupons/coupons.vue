<template>
	<view class="coupons-page">
		<!-- 页面标题 -->
		<view class="page-header">
			<text class="page-title">我的优惠券</text>
		</view>

		<!-- 状态标签页 -->
		<view class="tabs-container">
			<view class="tabs">
				<view 
					class="tab-item" 
					:class="{ active: activeTab === 'all' }"
					@click="switchTab('all')"
				>
					全部
				</view>
				<view 
					class="tab-item" 
					:class="{ active: activeTab === 'unused' }"
					@click="switchTab('unused')"
				>
					未使用
				</view>
				<view 
					class="tab-item" 
					:class="{ active: activeTab === 'used' }"
					@click="switchTab('used')"
				>
					已使用
				</view>
				<view 
					class="tab-item" 
					:class="{ active: activeTab === 'expired' }"
					@click="switchTab('expired')"
				>
					已过期
				</view>
			</view>
		</view>

		<!-- 优惠券列表 -->
		<view class="coupons-container">
			<view v-if="loading" class="loading-container">
				<text class="loading-text">加载中...</text>
			</view>

			<view v-else-if="coupons.length === 0" class="empty-container">
				<view class="empty-icon">🎫</view>
				<text class="empty-text">{{ getEmptyText() }}</text>
			</view>

			<view v-else class="coupons-list">
				<view 
					v-for="coupon in coupons" 
					:key="coupon.user_coupon_id"
					class="coupon-card"
					:class="getCouponCardClass(coupon.status)"
				>
					<!-- 优惠券主体 -->
					<view class="coupon-main">
						<!-- 左侧价值区域 -->
						<view class="coupon-value">
							<view class="value-container">
								<text v-if="coupon.type === 'fixed'" class="value-number">￥{{ coupon.value }}</text>
								<text v-else class="value-number">{{ getDiscountText(coupon.value) }}</text>
								<text class="value-label">{{ coupon.type === 'fixed' ? '立减' : '折扣' }}</text>
							</view>
						</view>

						<!-- 右侧信息区域 -->
						<view class="coupon-info">
							<view class="coupon-name">{{ coupon.name }}</view>
							<view class="coupon-desc">{{ coupon.description }}</view>
							<view class="coupon-time">
								<text class="time-label">领取时间：</text>
								<text class="time-value">{{ formatDate(coupon.issue_date) }}</text>
							</view>
							<view class="coupon-expiry">
								<text class="expiry-label">有效期至：</text>
								<text class="expiry-value">{{ formatDate(coupon.expiry_date) }}</text>
							</view>
						</view>

						<!-- 状态标识 -->
						<view class="coupon-status">
							<text class="status-text">{{ coupon.status_text }}</text>
						</view>
					</view>

					<!-- 已使用优惠券的订单信息 -->
					<view v-if="coupon.status === 'used' && coupon.used_at" class="coupon-footer">
						<text class="used-info">使用时间：{{ formatDateTime(coupon.used_at) }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载更多 -->
		<view v-if="hasMore && !loading" class="load-more" @click="loadMore">
			<text class="load-more-text">加载更多</text>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { getUserCoupons } from '../../api/user.js'

// 响应式数据
const activeTab = ref('all')
const coupons = ref([])
const loading = ref(false)
const currentPage = ref(1)
const totalPages = ref(1)
const hasMore = computed(() => currentPage.value < totalPages.value)

// 页面加载时获取数据
onMounted(() => {
	loadCoupons()
})

// 切换标签页
const switchTab = (tab: string) => {
	if (activeTab.value === tab) return
	
	activeTab.value = tab
	currentPage.value = 1
	coupons.value = []
	loadCoupons()
}

// 加载优惠券数据
const loadCoupons = async () => {
	if (loading.value) return
	
	loading.value = true
	
	try {
		const params: any = {
			page: currentPage.value,
			limit: 10
		}
		
		// 根据标签页设置状态筛选
		if (activeTab.value !== 'all') {
			params.status = activeTab.value
		}
		
		const response = await getUserCoupons(params)
		
		if (response.success) {
			const { list, totalPages: total } = response.data
			
			if (currentPage.value === 1) {
				coupons.value = list
			} else {
				coupons.value.push(...list)
			}
			
			totalPages.value = total
		}
	} catch (error) {
		console.error('获取优惠券列表失败:', error)
		uni.showToast({
			title: '获取优惠券失败',
			icon: 'none'
		})
	} finally {
		loading.value = false
	}
}

// 加载更多
const loadMore = () => {
	if (hasMore.value && !loading.value) {
		currentPage.value++
		loadCoupons()
	}
}

// 获取空状态文本
const getEmptyText = () => {
	switch (activeTab.value) {
		case 'unused':
			return '暂无未使用的优惠券'
		case 'used':
			return '暂无已使用的优惠券'
		case 'expired':
			return '暂无已过期的优惠券'
		default:
			return '暂无优惠券'
	}
}

// 获取优惠券卡片样式类
const getCouponCardClass = (status: string) => {
	switch (status) {
		case 'unused':
			return 'coupon-unused'
		case 'used':
			return 'coupon-used'
		case 'expired':
			return 'coupon-expired'
		default:
			return ''
	}
}

// 获取折扣文本
const getDiscountText = (value: number) => {
	const discount = Math.round((1 - value) * 10)
	return `${discount}折`
}

// 格式化日期
const formatDate = (dateString: string) => {
	if (!dateString) return ''
	const date = new Date(dateString)
	return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
	if (!dateString) return ''
	const date = new Date(dateString)
	return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}
</script>

<style scoped>
.coupons-page {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.page-header {
	background-color: #fff;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #eee;
}

.page-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.tabs-container {
	background-color: #fff;
	padding: 0 40rpx;
	border-bottom: 1rpx solid #eee;
}

.tabs {
	display: flex;
	align-items: center;
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 30rpx 0;
	font-size: 28rpx;
	color: #666;
	border-bottom: 4rpx solid transparent;
	transition: all 0.3s ease;
}

.tab-item.active {
	color: #007aff;
	border-bottom-color: #007aff;
	font-weight: bold;
}

.coupons-container {
	padding: 20rpx;
}

.loading-container,
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #999;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.coupons-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

/* 优惠券卡片基础样式 */
.coupon-card {
	background-color: #fff;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	position: relative;
}

.coupon-main {
	display: flex;
	align-items: stretch;
	position: relative;
}

/* 左侧价值区域 */
.coupon-value {
	width: 200rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
	color: #fff;
	position: relative;
}

.coupon-value::after {
	content: '';
	position: absolute;
	right: -10rpx;
	top: 50%;
	transform: translateY(-50%);
	width: 0;
	height: 0;
	border-left: 10rpx solid #ff6b6b;
	border-top: 10rpx solid transparent;
	border-bottom: 10rpx solid transparent;
}

.value-container {
	text-align: center;
}

.value-number {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	line-height: 1;
	margin-bottom: 8rpx;
}

.value-label {
	font-size: 24rpx;
	opacity: 0.9;
}

/* 右侧信息区域 */
.coupon-info {
	flex: 1;
	padding: 30rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.coupon-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 12rpx;
}

.coupon-desc {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 16rpx;
}

.coupon-time,
.coupon-expiry {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 8rpx;
}

.time-label,
.expiry-label {
	margin-right: 8rpx;
}

/* 状态标识 */
.coupon-status {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
}

/* 不同状态的样式 */
.coupon-unused .coupon-value {
	background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
}

.coupon-unused .coupon-value::after {
	border-left-color: #ff6b6b;
}

.coupon-unused .coupon-status {
	background-color: #e8f5e8;
	color: #52c41a;
}

.coupon-used .coupon-value {
	background: linear-gradient(135deg, #999, #bbb);
}

.coupon-used .coupon-value::after {
	border-left-color: #999;
}

.coupon-used .coupon-status {
	background-color: #f0f0f0;
	color: #999;
}

.coupon-expired .coupon-value {
	background: linear-gradient(135deg, #ccc, #ddd);
}

.coupon-expired .coupon-value::after {
	border-left-color: #ccc;
}

.coupon-expired .coupon-status {
	background-color: #fff2f0;
	color: #ff4d4f;
}

.coupon-expired {
	opacity: 0.6;
}

/* 底部信息 */
.coupon-footer {
	padding: 20rpx 30rpx;
	background-color: #f8f8f8;
	border-top: 1rpx solid #eee;
}

.used-info {
	font-size: 24rpx;
	color: #999;
}

/* 加载更多 */
.load-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	margin-top: 20rpx;
}

.load-more-text {
	font-size: 28rpx;
	color: #007aff;
}
</style>
