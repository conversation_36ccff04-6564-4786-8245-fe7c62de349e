<template>
	<view class="parking-list-page">
		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input-wrapper" @click="goToSearch">
				<text class="search-icon">🔍</text>
				<text class="search-placeholder">搜索停车场名称或地址</text>
			</view>
		</view>

		<!-- 筛选和排序栏 -->
		<view class="filter-sort-bar">
			<view class="filter-section">
				<view class="filter-item" @click="showCategoryFilter = true">
					<text class="filter-text">{{ selectedCategory ? selectedCategory.name : '分类' }}</text>
					<text class="filter-arrow">▼</text>
				</view>
				<view class="filter-item" @click="showPriceFilter = true">
					<text class="filter-text">{{ priceFilterText }}</text>
					<text class="filter-arrow">▼</text>
				</view>

			</view>
			<view class="sort-section">
				<view class="sort-item" @click="showSortOptions = true">
					<text class="sort-text">{{ sortText }}</text>
					<text class="sort-arrow">▼</text>
				</view>
			</view>
		</view>

		<!-- 停车场列表 -->
		<scroll-view
			class="parking-list"
			scroll-y="true"
			@scrolltolower="loadMore"
			refresher-enabled="true"
			:refresher-triggered="refreshing"
			@refresherrefresh="onRefresh"
		>
			<!-- 加载状态 -->
			<view v-if="loading && parkingList.length === 0" class="loading-container">
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 停车场卡片列表 -->
			<view v-else-if="parkingList.length > 0" class="parking-cards">
				<view
					class="parking-card"
					v-for="parking in parkingList"
					:key="parking.id"
					@click="goToDetail(parking.id)"
				>
					<!-- 停车场图片 -->
					<view class="card-image">
						<image
							:src="getImageUrl(parking.image_urls)"
							mode="aspectFill"
							class="parking-image"
						/>

					</view>

					<!-- 停车场信息 -->
					<view class="card-content">
						<view class="card-header">
							<text class="parking-name">{{ parking.name }}</text>
							<view class="rating-section" v-if="parking.rating">
								<text class="rating-score">{{ parking.rating }}</text>
								<text class="rating-star">⭐</text>
							</view>
						</view>

						<view class="parking-address">
							<text class="address-icon">📍</text>
							<text class="address-text">{{ parking.address }}</text>
						</view>

						<view class="parking-info">
							<view class="info-item">
								<text class="info-label">分类：</text>
								<text class="info-value">{{ parking.category_name || '通用' }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">车位：</text>
								<text class="info-value">{{ parking.total_spaces || 0 }}个</text>
							</view>
						</view>

						<view class="card-footer">
							<view class="price-section">
								<text class="price-label">{{ getPriceLabel(parking) }}：</text>
								<text class="price-value">¥{{ formatPrice(parking) }}{{ getPriceUnit(parking) }}</text>
							</view>
							<view class="status-section">
								<text class="status-text status-available">
									营业中
								</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-else-if="!loading" class="empty-container">
				<text class="empty-icon">🚗</text>
				<text class="empty-text">暂无停车场数据</text>
				<text class="empty-tip">试试调整筛选条件</text>
			</view>

			<!-- 加载更多 -->
			<view v-if="loading && parkingList.length > 0" class="load-more">
				<text class="load-more-text">加载中...</text>
			</view>

			<!-- 没有更多数据 -->
			<view v-if="!hasMore && parkingList.length > 0" class="no-more">
				<text class="no-more-text">没有更多数据了</text>
			</view>
		</scroll-view>

		<!-- 分类筛选弹窗 -->
		<view v-if="showCategoryFilter" class="filter-modal" @click="showCategoryFilter = false">
			<view class="filter-content" @click.stop>
				<view class="filter-header">
					<text class="filter-title">选择分类</text>
					<text class="filter-close" @click="showCategoryFilter = false">✕</text>
				</view>
				<view class="filter-options">
					<view
						class="filter-option"
						:class="{ active: !selectedCategory }"
						@click="selectCategory(null)"
					>
						<text class="option-text">全部分类</text>
					</view>
					<view
						class="filter-option"
						v-for="category in categories"
						:key="category.id"
						:class="{ active: selectedCategory && selectedCategory.id === category.id }"
						@click="selectCategory(category)"
					>
						<text class="option-text">{{ category.name }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 价格筛选弹窗 -->
		<view v-if="showPriceFilter" class="filter-modal" @click="showPriceFilter = false">
			<view class="filter-content" @click.stop>
				<view class="filter-header">
					<text class="filter-title">价格范围</text>
					<text class="filter-close" @click="showPriceFilter = false">✕</text>
				</view>
				<view class="filter-options">
					<view
						class="filter-option"
						:class="{ active: !priceRange.min && !priceRange.max }"
						@click="selectPriceRange(null, null)"
					>
						<text class="option-text">不限价格</text>
					</view>
					<view
						class="filter-option"
						:class="{ active: priceRange.min === 0 && priceRange.max === 5 }"
						@click="selectPriceRange(0, 5)"
					>
						<text class="option-text">¥0-5/小时</text>
					</view>
					<view
						class="filter-option"
						:class="{ active: priceRange.min === 5 && priceRange.max === 10 }"
						@click="selectPriceRange(5, 10)"
					>
						<text class="option-text">¥5-10/小时</text>
					</view>
					<view
						class="filter-option"
						:class="{ active: priceRange.min === 10 && priceRange.max === 20 }"
						@click="selectPriceRange(10, 20)"
					>
						<text class="option-text">¥10-20/小时</text>
					</view>
					<view
						class="filter-option"
						:class="{ active: priceRange.min === 20 && !priceRange.max }"
						@click="selectPriceRange(20, null)"
					>
						<text class="option-text">¥20以上/小时</text>
					</view>
				</view>
			</view>
		</view>


		<!-- 排序选项弹窗 -->
		<view v-if="showSortOptions" class="filter-modal" @click="showSortOptions = false">
			<view class="filter-content" @click.stop>
				<view class="filter-header">
					<text class="filter-title">排序方式</text>
					<text class="filter-close" @click="showSortOptions = false">✕</text>
				</view>
				<view class="filter-options">

					<view
						class="filter-option"
						:class="{ active: sortBy === 'price' }"
						@click="selectSort('price')"
					>
						<text class="option-text">按价格排序</text>
					</view>
					<view
						class="filter-option"
						:class="{ active: sortBy === 'rating' }"
						@click="selectSort('rating')"
					>
						<text class="option-text">按评分排序</text>
					</view>
					<view
						class="filter-option"
						:class="{ active: sortBy === 'created_at' }"
						@click="selectSort('created_at')"
					>
						<text class="option-text">按最新排序</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onPullDownRefresh, onShow, onLoad } from '@dcloudio/uni-app'
import { getParkingLots, getParkingCategories } from '../../api/parking.js'
import { getFullImageUrl } from '../../utils/request.js'

// 响应式数据
const parkingList = ref([])
const categories = ref([])
const loading = ref(false)
const refreshing = ref(false)
const currentPage = ref(1)
const hasMore = ref(true)

// 页面参数
const pageParams = ref({
	type: '', // 'airport' 或 'railway'
	categoryId: null, // 分类ID
	categoryName: '' // 分类名称
})


// 筛选和排序状态
const selectedCategory = ref(null)
const priceRange = ref({ min: null, max: null })
const sortBy = ref('created_at')

// 弹窗显示状态
const showCategoryFilter = ref(false)
const showPriceFilter = ref(false)
const showSortOptions = ref(false)

// 计算属性
const priceFilterText = computed(() => {
	if (!priceRange.value.min && !priceRange.value.max) {
		return '价格'
	}
	if (priceRange.value.min && priceRange.value.max) {
		return `¥${priceRange.value.min}-${priceRange.value.max}`
	}
	if (priceRange.value.min && !priceRange.value.max) {
		return `¥${priceRange.value.min}以上`
	}
	return '价格'
})



const sortText = computed(() => {
	const sortMap = {
		'price': '价格',
		'rating': '评分',
		'created_at': '最新'
	}
	return sortMap[sortBy.value] || '排序'
})

// 页面方法
const goToSearch = () => {
	uni.navigateTo({
		url: '/pages/search/search'
	})
}

const goToDetail = (id) => {
	uni.navigateTo({
		url: `/pages/detail/detail?id=${id}`
	})
}



// 格式化图片URL
const getImageUrl = (images) => {
	if (!images) return '/static/parking-default.svg'

	let imageList = []
	if (typeof images === 'string') {
		try {
			imageList = JSON.parse(images)
		} catch {
			imageList = [images]
		}
	} else if (Array.isArray(images)) {
		imageList = images
	} else {
		return '/static/parking-default.svg'
	}

	if (imageList.length > 0 && imageList[0]) {
		return getFullImageUrl(imageList[0])
	}
	return '/static/parking-default.svg'
}



// 检查是否为累计天数收费模式
const isCumulativePricing = (parking) => {
	return parking.price_rules && 
		   typeof parking.price_rules === 'object' && 
		   parking.price_rules.type === 'cumulative_daily'
}

// 获取价格标签
const getPriceLabel = (parking) => {
	if (isCumulativePricing(parking)) {
		return '累计价格'
	}
	return '起步价'
}

// 获取价格单位
const getPriceUnit = (parking) => {
	if (isCumulativePricing(parking)) {
		return '/天'
	}
	return '/小时'
}

// 格式化价格显示
const formatPrice = (parking) => {
	// 检查是否有新的累计天数价格配置
	if (isCumulativePricing(parking)) {
		const dailyPrices = parking.price_rules.daily_prices
		if (dailyPrices) {
			const day1Price = dailyPrices.day_1 || 0
			const day7Price = dailyPrices.day_7 || 0
			
			if (day1Price === 0) {
				return `首日免费`
			} else if (day1Price === day7Price) {
				return `${day1Price.toFixed(2)}`
			} else {
				return `${day1Price.toFixed(2)}-${day7Price.toFixed(2)}`
			}
		}
	}

	// 兼容旧的小时费率价格规则
	if (parking.price_rules && Array.isArray(parking.price_rules) && parking.price_rules.length > 0) {
		const prices = parking.price_rules.map((rule: any) => Number(rule.price_per_hour) || 0)
		const minPrice = Math.min(...prices)
		const maxPrice = Math.max(...prices)

		if (minPrice === maxPrice) {
			return minPrice.toFixed(2)
		} else {
			return `${minPrice.toFixed(2)}-${maxPrice.toFixed(2)}`
		}
	}

	// 兼容旧的 hourly_rate 字段
	if (parking.hourly_rate) {
		return Number(parking.hourly_rate).toFixed(2)
	}

	// 兼容旧的 price 字段
	if (parking.price) {
		return Number(parking.price).toFixed(2)
	}

	return '面议'
}



// 筛选和排序方法
const selectCategory = (category: any) => {
	selectedCategory.value = category
	showCategoryFilter.value = false
	resetAndLoadData()
}

const selectPriceRange = (min: number | null, max: number | null) => {
	priceRange.value = { min, max }
	showPriceFilter.value = false
	resetAndLoadData()
}



const selectSort = (sort: string) => {
	sortBy.value = sort
	showSortOptions.value = false
	resetAndLoadData()
}

// 重置并加载数据
const resetAndLoadData = () => {
	currentPage.value = 1
	hasMore.value = true
	parkingList.value = []
	loadParkingList()
}

// 加载停车场分类
const loadCategories = async () => {
	try {
		const response = await getParkingCategories()
		if (response.success) {
			categories.value = response.data || []
		}
	} catch (error) {
		console.error('获取分类失败:', error)
	}
}

// 加载停车场列表
const loadParkingList = async (isLoadMore = false) => {
	if (loading.value) return

	loading.value = true

	try {
		// 构建查询参数
		const params: any = {
			page: currentPage.value,
			limit: 10
		}

		// 添加分类筛选
		if (selectedCategory.value) {
			params.categoryId = selectedCategory.value.id
		}

		// 添加排序
		params.sortBy = sortBy.value



		const response = await getParkingLots(params)

		if (response.success) {
			let newData = response.data.list || response.data || []

			// 价格筛选（前端过滤）
			if (priceRange.value.min !== null || priceRange.value.max !== null) {
				newData = newData.filter(item => {
					// 检查是否为累计天数收费模式
					if (isCumulativePricing(item)) {
						const dailyPrices = item.price_rules.daily_prices
						if (dailyPrices) {
							const day1Price = dailyPrices.day_1 || 0
							const day7Price = dailyPrices.day_7 || 0
							
							// 对于累计天数收费，使用第1天价格进行筛选
							const priceToCheck = day1Price
							
							if (priceRange.value.min !== null && priceToCheck < priceRange.value.min) {
								return false
							}
							if (priceRange.value.max !== null && priceToCheck > priceRange.value.max) {
								return false
							}
							return true
						}
					}

					// 兼容旧的小时费率价格规则
					if (item.price_rules && Array.isArray(item.price_rules) && item.price_rules.length > 0) {
						const prices = item.price_rules.map(rule => Number(rule.price_per_hour) || 0)
						const minPrice = Math.min(...prices)
						const maxPrice = Math.max(...prices)

						if (priceRange.value.min !== null && maxPrice < priceRange.value.min) {
							return false
						}
						if (priceRange.value.max !== null && minPrice > priceRange.value.max) {
							return false
						}
						return true
					}

					// 兼容旧的hourly_rate字段
					const price = parseFloat(item.hourly_rate) || 0
					if (priceRange.value.min !== null && price < priceRange.value.min) {
						return false
					}
					if (priceRange.value.max !== null && price > priceRange.value.max) {
						return false
					}
					return true
				})
			}

			// 前端排序（针对价格和评分）
			if (sortBy.value === 'price' || sortBy.value === 'rating') {
				newData = newData.sort((a, b) => {
					if (sortBy.value === 'price') {
						const getPriceForSort = (item) => {
							// 检查是否为累计天数收费模式
							if (isCumulativePricing(item)) {
								const dailyPrices = item.price_rules.daily_prices
								if (dailyPrices) {
									// 使用第1天价格进行排序
									return dailyPrices.day_1 || 0
								}
							}

							// 兼容旧的小时费率价格规则
							if (item.price_rules && Array.isArray(item.price_rules) && item.price_rules.length > 0) {
								const prices = item.price_rules.map(rule => Number(rule.price_per_hour) || 0)
								return Math.min(...prices) // 按最低价格排序
							}
							
							// 兼容旧的hourly_rate字段
							return parseFloat(item.hourly_rate) || 0
						}
						return getPriceForSort(a) - getPriceForSort(b) // 价格从低到高
					}

					if (sortBy.value === 'rating') {
						const ratingA = parseFloat(a.rating) || 0
						const ratingB = parseFloat(b.rating) || 0
						return ratingB - ratingA // 评分从高到低
					}

					return 0
				})
			}

			hasMore.value = response.data.page < response.data.totalPages

			if (isLoadMore) {
				parkingList.value = [...parkingList.value, ...newData]
			} else {
				parkingList.value = newData
			}
		}
	} catch (error) {
		console.error('获取停车场列表失败:', error)
		uni.showToast({
			title: '获取数据失败',
			icon: 'none'
		})
	} finally {
		loading.value = false
		refreshing.value = false
	}
}

// 加载更多
const loadMore = () => {
	if (!hasMore.value || loading.value) return

	currentPage.value++
	loadParkingList(true)
}

// 下拉刷新
const onRefresh = () => {
	refreshing.value = true
	resetAndLoadData()
}

// 页面加载时处理参数
onLoad((options) => {
	console.log('列表页接收到的参数:', options)

	// 处理页面参数
	if (options.type) {
		pageParams.value.type = options.type
	}
	if (options.categoryId) {
		pageParams.value.categoryId = parseInt(options.categoryId)
		// 如果有分类ID，设置为选中状态
		selectedCategory.value = { id: parseInt(options.categoryId) }
	}
	if (options.categoryName) {
		pageParams.value.categoryName = decodeURIComponent(options.categoryName)
		// 更新选中分类的名称
		if (selectedCategory.value) {
			selectedCategory.value.name = pageParams.value.categoryName
		}
	}
})

// 生命周期
onMounted(async () => {
	// 加载分类数据
	await loadCategories()

	// 加载停车场列表
	await loadParkingList()
})

onShow(() => {
	// 检查是否有从分类选择页传来的参数
	const storedParams = uni.getStorageSync('listPageParams')
	if (storedParams) {
		console.log('从本地存储获取到参数:', storedParams)

		// 更新页面参数
		pageParams.value.type = storedParams.type || ''
		pageParams.value.categoryId = storedParams.categoryId || null
		pageParams.value.categoryName = storedParams.categoryName || ''

		// 如果有分类ID，设置为选中状态
		if (storedParams.categoryId) {
			selectedCategory.value = {
				id: storedParams.categoryId,
				name: storedParams.categoryName
			}
		}

		// 清除存储的参数，避免重复使用
		uni.removeStorageSync('listPageParams')

		// 重新加载数据
		resetAndLoadData()
	} else {
		// 页面显示时刷新数据
		resetAndLoadData()
	}
})

// 下拉刷新处理
onPullDownRefresh(() => {
	onRefresh()
	uni.stopPullDownRefresh()
})
</script>

<style scoped>
.parking-list-page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 搜索栏样式 */
.search-bar {
	background-color: #fff;
	padding: 20rpx;
	border-bottom: 1rpx solid #eee;
}

.search-input-wrapper {
	background-color: #f8f8f8;
	border-radius: 50rpx;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
}

.search-icon {
	font-size: 32rpx;
	color: #999;
	margin-right: 20rpx;
}

.search-placeholder {
	font-size: 28rpx;
	color: #999;
	flex: 1;
}

/* 筛选和排序栏样式 */
.filter-sort-bar {
	background-color: #fff;
	padding: 20rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1rpx solid #eee;
}

.filter-section {
	display: flex;
	gap: 20rpx;
}

.filter-item, .sort-item {
	padding: 12rpx 24rpx;
	background-color: #f8f8f8;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.filter-text, .sort-text {
	font-size: 26rpx;
	color: #333;
}

.filter-arrow, .sort-arrow {
	font-size: 20rpx;
	color: #666;
}

/* 停车场列表样式 */
.parking-list {
	flex: 1;
	padding: 20rpx;
}

.parking-cards {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.parking-card {
	background-color: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-image {
	position: relative;
	height: 300rpx;
}

.parking-image {
	width: 100%;
	height: 100%;
}



.card-content {
	padding: 24rpx;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 16rpx;
}

.parking-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	flex: 1;
	margin-right: 20rpx;
}

.rating-section {
	display: flex;
	align-items: center;
	gap: 4rpx;
}

.rating-score {
	font-size: 28rpx;
	color: #ff6b35;
	font-weight: bold;
}

.rating-star {
	font-size: 24rpx;
}

.parking-address {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.address-icon {
	font-size: 24rpx;
	margin-right: 8rpx;
}

.address-text {
	font-size: 26rpx;
	color: #666;
	flex: 1;
}

.parking-info {
	display: flex;
	gap: 40rpx;
	margin-bottom: 20rpx;
}

.info-item {
	display: flex;
	align-items: center;
}

.info-label {
	font-size: 24rpx;
	color: #999;
}

.info-value {
	font-size: 24rpx;
	color: #333;
}

.card-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.price-section {
	display: flex;
	align-items: baseline;
	gap: 8rpx;
}

.price-label {
	font-size: 24rpx;
	color: #999;
}

.price-value {
	font-size: 32rpx;
	color: #ff6b35;
	font-weight: bold;
}

.status-section {
	display: flex;
	align-items: center;
}

.status-text {
	font-size: 24rpx;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-weight: bold;
}

.status-available {
	background-color: #e8f5e8;
	color: #52c41a;
}

.status-limited {
	background-color: #fff7e6;
	color: #fa8c16;
}

.status-full {
	background-color: #fff2f0;
	color: #ff4d4f;
}

.status-unknown {
	background-color: #f0f0f0;
	color: #999;
}

/* 加载和空状态样式 */
.loading-container, .empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #999;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 32rpx;
	color: #333;
	margin-bottom: 12rpx;
}

.empty-tip {
	font-size: 26rpx;
	color: #999;
}

.load-more, .no-more {
	display: flex;
	justify-content: center;
	padding: 40rpx;
}

.load-more-text, .no-more-text {
	font-size: 26rpx;
	color: #999;
}

/* 筛选弹窗样式 */
.filter-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: flex-end;
	z-index: 1000;
}

.filter-content {
	background-color: #fff;
	width: 100%;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 80vh;
	overflow: hidden;
}

.filter-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #eee;
}

.filter-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.filter-close {
	font-size: 40rpx;
	color: #999;
	padding: 10rpx;
}

.filter-options {
	padding: 20rpx 0;
	max-height: 60vh;
	overflow-y: auto;
}

.filter-option {
	padding: 30rpx 40rpx;
	display: flex;
	align-items: center;
	border-bottom: 1rpx solid #f5f5f5;
}

.filter-option.active {
	background-color: #f0f9ff;
}

.filter-option.active .option-text {
	color: #1890ff;
	font-weight: bold;
}

.option-text {
	font-size: 30rpx;
	color: #333;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.filter-section {
		gap: 15rpx;
	}

	.filter-item, .sort-item {
		padding: 10rpx 20rpx;
	}

	.filter-text, .sort-text {
		font-size: 24rpx;
	}

	.parking-cards {
		gap: 15rpx;
	}

	.card-content {
		padding: 20rpx;
	}

	.parking-name {
		font-size: 30rpx;
	}

	.price-value {
		font-size: 28rpx;
	}
}
</style>
