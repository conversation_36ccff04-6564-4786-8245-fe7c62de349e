<template>
	<view class="container">
		<!-- 页面标题 -->
		<view class="header">
			<text class="header-title">我的车辆</text>
			<view class="header-action">
				<button class="add-btn" @click="goToAddVehicle">
					<text class="add-icon">+</text>
					<text class="add-text">添加车辆</text>
				</button>
			</view>
		</view>

		<!-- 车辆列表 -->
		<view class="vehicle-list" v-if="vehicles.length > 0">
			<view 
				class="vehicle-item" 
				v-for="vehicle in vehicles" 
				:key="vehicle.id"
				@click="selectVehicle(vehicle)"
			>
				<view class="vehicle-info">
					<view class="vehicle-plate">
						<text class="plate-number">{{ vehicle.plate_number }}</text>
						<view class="default-badge" v-if="vehicle.is_default">
							<text class="badge-text">默认</text>
						</view>
					</view>
					<view class="vehicle-details">
						<text class="vehicle-type">{{ getVehicleTypeName(vehicle.vehicle_type) }}</text>
						<text class="vehicle-brand" v-if="vehicle.brand || vehicle.model">
							{{ vehicle.brand }} {{ vehicle.model }}
						</text>
						<text class="vehicle-color" v-if="vehicle.color">{{ vehicle.color }}</text>
					</view>
				</view>
				<view class="vehicle-actions">
					<button class="action-btn edit-btn" @click.stop="editVehicle(vehicle)">编辑</button>
					<button class="action-btn default-btn" v-if="!vehicle.is_default" @click.stop="setDefault(vehicle)">
						设为默认
					</button>
					<button class="action-btn delete-btn" @click.stop="deleteVehicle(vehicle)">删除</button>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-else>
			<view class="empty-icon">🚗</view>
			<text class="empty-title">还没有添加车辆</text>
			<text class="empty-desc">添加您的车辆信息，预定停车更便捷</text>
			<button class="empty-add-btn" @click="goToAddVehicle">立即添加</button>
		</view>

		<!-- 删除确认弹窗 -->
		<view class="delete-modal" v-if="showDeleteModal" @click="closeDeleteModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">确认删除</text>
				</view>
				<view class="modal-body">
					<text class="modal-text">确定要删除车辆 {{ deleteTarget?.plate_number }} 吗？</text>
					<text class="modal-warning" v-if="deleteTarget?.is_default">
						删除默认车辆后，将自动设置其他车辆为默认车辆
					</text>
				</view>
				<view class="modal-footer">
					<button class="modal-btn cancel-btn" @click="closeDeleteModal">取消</button>
					<button class="modal-btn confirm-btn" @click="confirmDelete">确定删除</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import vehicleStorage from '../../utils/vehicleStorage.js'
import vehicleValidator from '../../utils/vehicleValidator.js'

// 响应式数据
const vehicles = ref([])
const showDeleteModal = ref(false)
const deleteTarget = ref(null)
const loading = ref(false)

// 获取车辆类型显示名称
const getVehicleTypeName = (type) => {
	return vehicleValidator.getVehicleTypeName(type)
}

// 加载车辆列表
const loadVehicles = () => {
	try {
		loading.value = true
		const vehicleList = vehicleStorage.getAllVehicles()
		vehicles.value = vehicleList
		console.log('车辆列表加载成功:', vehicleList)
	} catch (error) {
		console.error('加载车辆列表失败:', error)
		uni.showToast({
			title: '加载失败，请重试',
			icon: 'none'
		})
	} finally {
		loading.value = false
	}
}

// 跳转到添加车辆页面
const goToAddVehicle = () => {
	uni.navigateTo({
		url: '/pages/vehicles/form'
	})
}

// 编辑车辆
const editVehicle = (vehicle) => {
	uni.navigateTo({
		url: `/pages/vehicles/form?id=${vehicle.id}&mode=edit`
	})
}

// 选择车辆（可用于其他功能扩展）
const selectVehicle = (vehicle) => {
	console.log('选择车辆:', vehicle)
	// 这里可以添加选择车辆的逻辑，比如返回给调用页面
}

// 设置默认车辆
const setDefault = async (vehicle) => {
	try {
		uni.showLoading({
			title: '设置中...',
			mask: true
		})

		const result = vehicleStorage.setDefaultVehicle(vehicle.id)
		
		uni.hideLoading()

		if (result.success) {
			uni.showToast({
				title: result.message,
				icon: 'success'
			})
			loadVehicles() // 重新加载列表
		} else {
			uni.showToast({
				title: result.message,
				icon: 'none'
			})
		}
	} catch (error) {
		uni.hideLoading()
		console.error('设置默认车辆失败:', error)
		uni.showToast({
			title: '设置失败，请重试',
			icon: 'none'
		})
	}
}

// 删除车辆
const deleteVehicle = (vehicle) => {
	deleteTarget.value = vehicle
	showDeleteModal.value = true
}

// 关闭删除确认弹窗
const closeDeleteModal = () => {
	showDeleteModal.value = false
	deleteTarget.value = null
}

// 确认删除
const confirmDelete = async () => {
	if (!deleteTarget.value) return

	try {
		uni.showLoading({
			title: '删除中...',
			mask: true
		})

		const result = vehicleStorage.deleteVehicle(deleteTarget.value.id)
		
		uni.hideLoading()

		if (result.success) {
			uni.showToast({
				title: result.message,
				icon: 'success'
			})
			loadVehicles() // 重新加载列表
		} else {
			uni.showToast({
				title: result.message,
				icon: 'none'
			})
		}
	} catch (error) {
		uni.hideLoading()
		console.error('删除车辆失败:', error)
		uni.showToast({
			title: '删除失败，请重试',
			icon: 'none'
		})
	} finally {
		closeDeleteModal()
	}
}

// 页面加载时获取车辆列表
onMounted(() => {
	loadVehicles()
})

// 页面显示时重新加载（从添加/编辑页面返回时）
onShow(() => {
	loadVehicles()
})
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	background-color: #fff;
	padding: 30rpx 40rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1rpx solid #eee;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.add-btn {
	display: flex;
	align-items: center;
	background-color: #007aff;
	color: #fff;
	border: none;
	border-radius: 50rpx;
	padding: 16rpx 24rpx;
	font-size: 28rpx;
}

.add-icon {
	font-size: 32rpx;
	margin-right: 8rpx;
}

.add-text {
	font-size: 28rpx;
}

.vehicle-list {
	padding: 20rpx;
}

.vehicle-item {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.vehicle-info {
	margin-bottom: 20rpx;
}

.vehicle-plate {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.plate-number {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-right: 16rpx;
}

.default-badge {
	background-color: #ff6b35;
	border-radius: 20rpx;
	padding: 4rpx 12rpx;
}

.badge-text {
	font-size: 20rpx;
	color: #fff;
}

.vehicle-details {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.vehicle-type {
	font-size: 28rpx;
	color: #666;
}

.vehicle-brand {
	font-size: 26rpx;
	color: #888;
}

.vehicle-color {
	font-size: 26rpx;
	color: #888;
}

.vehicle-actions {
	display: flex;
	gap: 16rpx;
}

.action-btn {
	flex: 1;
	padding: 16rpx 20rpx;
	border: none;
	border-radius: 8rpx;
	font-size: 26rpx;
	text-align: center;
}

.edit-btn {
	background-color: #007aff;
	color: #fff;
}

.default-btn {
	background-color: #28a745;
	color: #fff;
}

.delete-btn {
	background-color: #dc3545;
	color: #fff;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	text-align: center;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 40rpx;
	opacity: 0.6;
}

.empty-title {
	font-size: 32rpx;
	color: #333;
	margin-bottom: 16rpx;
}

.empty-desc {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 60rpx;
	line-height: 1.5;
}

.empty-add-btn {
	background-color: #007aff;
	color: #fff;
	border: none;
	border-radius: 50rpx;
	padding: 24rpx 48rpx;
	font-size: 30rpx;
}

.delete-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	background-color: #fff;
	border-radius: 16rpx;
	width: 80%;
	max-width: 600rpx;
	overflow: hidden;
}

.modal-header {
	padding: 40rpx 30rpx 20rpx;
	text-align: center;
	border-bottom: 1rpx solid #eee;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.modal-body {
	padding: 30rpx;
	text-align: center;
}

.modal-text {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 16rpx;
	line-height: 1.5;
}

.modal-warning {
	font-size: 24rpx;
	color: #ff6b35;
	line-height: 1.4;
}

.modal-footer {
	display: flex;
	border-top: 1rpx solid #eee;
}

.modal-btn {
	flex: 1;
	padding: 30rpx;
	border: none;
	font-size: 30rpx;
	text-align: center;
}

.cancel-btn {
	background-color: #f8f9fa;
	color: #666;
	border-right: 1rpx solid #eee;
}

.confirm-btn {
	background-color: #dc3545;
	color: #fff;
}
</style>