<template>
	<view class="container">
		<!-- 页面头部 -->
		<view class="page-header">
			<view class="header-icon">📞</view>
			<text class="page-title">联系客服</text>
			<text class="page-subtitle">我们随时为您提供帮助</text>
		</view>

		<!-- 联系方式 -->
		<view class="section">
			<view class="section-header">
				<view class="section-icon">📱</view>
				<text class="section-title">联系方式</text>
			</view>
			<view class="contact-list">
				<view class="contact-item" @click="makePhoneCall">
					<view class="contact-icon">📞</view>
					<view class="contact-content">
						<text class="contact-title">客服热线</text>
						<text class="contact-value">{{ serviceConfig.phone }}</text>
						<text class="contact-desc">工作时间：9:00-18:00</text>
					</view>
					<text class="contact-arrow">></text>
				</view>
				
				<view class="contact-item" @click="copyWechat">
					<view class="contact-icon">💬</view>
					<view class="contact-content">
						<text class="contact-title">微信客服</text>
						<text class="contact-value">{{ serviceConfig.wechat }}</text>
						<text class="contact-desc">点击复制微信号</text>
					</view>
					<text class="contact-arrow">></text>
				</view>
			</view>
		</view>

		<!-- 微信二维码 -->
		<view class="section">
			<view class="section-header">
				<view class="section-icon">📷</view>
				<text class="section-title">微信客服二维码</text>
			</view>
			<view class="qrcode-container">
				<view v-if="serviceConfig.qrcode" class="qrcode-image-container" @longpress="previewQRCode">
					<image 
						class="qrcode-image" 
						:src="serviceConfig.qrcode" 
						mode="aspectFit"
						@error="handleQRCodeError"
					></image>
					<text class="qrcode-desc">长按识别二维码添加客服微信</text>
				</view>
				<view v-else class="qrcode-placeholder">
					<text class="qrcode-text">微信客服二维码</text>
					<text class="qrcode-desc">管理员暂未配置二维码</text>
				</view>
			</view>
		</view>

		<!-- 常见问题快速入口 -->
		<view class="section">
			<view class="section-header">
				<view class="section-icon">❓</view>
				<text class="section-title">常见问题</text>
			</view>
			<view class="quick-help-list">
				<view class="help-item" v-for="help in quickHelp" :key="help.title" @click="handleQuickHelp(help)">
					<view class="help-icon">{{ help.icon }}</view>
					<view class="help-content">
						<text class="help-title">{{ help.title }}</text>
						<text class="help-desc">{{ help.description }}</text>
					</view>
					<text class="help-arrow">></text>
				</view>
			</view>
		</view>

		<!-- 服务时间 -->
		<view class="section">
			<view class="section-header">
				<view class="section-icon">⏰</view>
				<text class="section-title">服务时间</text>
			</view>
			<view class="service-time">
				<view class="time-item">
					<text class="time-label">客服热线</text>
					<text class="time-value">周一至周日 9:00-18:00</text>
				</view>
				<view class="time-item">
					<text class="time-label">微信客服</text>
					<text class="time-value">7×24小时在线</text>
				</view>
				<view class="time-item">
					<text class="time-label">紧急情况</text>
					<text class="time-value">随时联系停车场管理员</text>
				</view>
			</view>
		</view>

		<!-- 反馈建议 -->
		<view class="feedback-section">
			<text class="feedback-title">意见反馈</text>
			<text class="feedback-desc">您的建议是我们改进的动力</text>
			<button class="feedback-btn" @click="showFeedbackModal">
				<text class="feedback-btn-text">提交反馈</text>
			</button>
		</view>

		<!-- 反馈弹窗 -->
		<view class="modal-overlay" v-if="showModal" @click="hideModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">意见反馈</text>
					<text class="modal-close" @click="hideModal">×</text>
				</view>
				<view class="modal-body">
					<textarea 
						class="feedback-textarea" 
						v-model="feedbackText" 
						placeholder="请输入您的意见或建议..."
						maxlength="500"
					></textarea>
					<text class="char-count">{{ feedbackText.length }}/500</text>
				</view>
				<view class="modal-footer">
					<button class="modal-btn cancel-btn" @click="hideModal">取消</button>
					<button class="modal-btn submit-btn" @click="submitFeedback">提交</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getCustomerServiceConfig } from '../../api/service.js'
import { getFullImageUrl } from '../../utils/request.js'

// 响应式数据
const showModal = ref(false)
const feedbackText = ref('')
const serviceConfig = ref({
  wechat: 'parking_service',
  phone: '************',
  qrcode: ''
})
const loading = ref(false)

// 快速帮助选项
const quickHelp = ref([
	{
		icon: '🚗',
		title: '如何预定停车位？',
		description: '查看详细的预定流程说明',
		action: 'guide'
	},
	{
		icon: '💳',
		title: '支付相关问题',
		description: '支付失败、退款等问题',
		action: 'payment'
	},
	{
		icon: '🎫',
		title: '优惠券使用',
		description: '优惠券领取和使用说明',
		action: 'coupon'
	},
	{
		icon: '📋',
		title: '订单问题',
		description: '订单取消、修改等问题',
		action: 'order'
	}
])

// 获取客服配置
const loadServiceConfig = async () => {
	try {
		loading.value = true
		const response = await getCustomerServiceConfig()
		
		if (response.success) {
			const data = response.data
			serviceConfig.value = {
				...serviceConfig.value,
				...data,
				// 处理二维码图片URL，确保能正确显示
				qrcode: data.qrcode ? getFullImageUrl(data.qrcode) : ''
			}
		}
	} catch (error) {
		console.error('获取客服配置失败:', error)
		// 使用默认配置，不显示错误提示
	} finally {
		loading.value = false
	}
}

// 拨打电话
const makePhoneCall = () => {
	uni.makePhoneCall({
		phoneNumber: serviceConfig.value.phone,
		fail: (error) => {
			console.error('拨打电话失败:', error)
			uni.showToast({
				title: '拨打失败，请手动拨打',
				icon: 'none',
				duration: 2000
			})
		}
	})
}

// 复制微信号
const copyWechat = () => {
	uni.setClipboardData({
		data: serviceConfig.value.wechat,
		success: () => {
			uni.showToast({
				title: '微信号已复制',
				icon: 'success',
				duration: 2000
			})
		},
		fail: (error) => {
			console.error('复制失败:', error)
			uni.showToast({
				title: '复制失败',
				icon: 'none',
				duration: 2000
			})
		}
	})
}

// 处理快速帮助
const handleQuickHelp = (help) => {
	switch (help.action) {
		case 'guide':
			uni.navigateTo({
				url: '/pages/service-guide/service-guide'
			})
			break
		case 'payment':
		case 'coupon':
		case 'order':
			uni.showModal({
				title: help.title,
				content: '如需详细帮助，请联系客服或查看服务流程介绍',
				showCancel: true,
				cancelText: '联系客服',
				confirmText: '查看流程',
				success: (res) => {
					if (res.cancel) {
						// 联系客服 - 可以直接拨打电话或复制微信
						makePhoneCall()
					} else if (res.confirm) {
						// 查看流程
						uni.navigateTo({
							url: '/pages/service-guide/service-guide'
						})
					}
				}
			})
			break
		default:
			uni.showToast({
				title: '功能开发中',
				icon: 'none',
				duration: 2000
			})
	}
}

// 显示反馈弹窗
const showFeedbackModal = () => {
	showModal.value = true
}

// 隐藏反馈弹窗
const hideModal = () => {
	showModal.value = false
	feedbackText.value = ''
}

// 提交反馈
const submitFeedback = () => {
	if (!feedbackText.value.trim()) {
		uni.showToast({
			title: '请输入反馈内容',
			icon: 'none',
			duration: 2000
		})
		return
	}

	// 这里可以调用API提交反馈
	console.log('提交反馈:', feedbackText.value)
	
	uni.showToast({
		title: '反馈提交成功',
		icon: 'success',
		duration: 2000
	})
	
	hideModal()
}

// 预览二维码
const previewQRCode = () => {
	if (serviceConfig.value.qrcode) {
		uni.previewImage({
			urls: [serviceConfig.value.qrcode],
			current: serviceConfig.value.qrcode
		})
	}
}

// 处理二维码加载错误
const handleQRCodeError = () => {
	console.error('二维码加载失败')
	uni.showToast({
		title: '二维码加载失败',
		icon: 'none',
		duration: 2000
	})
}

// 页面加载时获取客服配置
onMounted(() => {
	loadServiceConfig()
})
</script>

<style scoped>
.container {
	min-height: 100vh;
	background-color: #f5f5f5;
}

/* 页面头部 */
.page-header {
	background: linear-gradient(135deg, #007AFF, #5AC8FA);
	padding: 60rpx 40rpx 40rpx;
	text-align: center;
	color: white;
}

.header-icon {
	font-size: 60rpx;
	margin-bottom: 20rpx;
}

.page-title {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 15rpx;
}

.page-subtitle {
	display: block;
	font-size: 28rpx;
	opacity: 0.9;
}

/* 通用section样式 */
.section {
	background: white;
	margin: 20rpx;
	border-radius: 20rpx;
	overflow: hidden;
}

.section-header {
	display: flex;
	align-items: center;
	padding: 30rpx 40rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.section-icon {
	font-size: 36rpx;
	margin-right: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

/* 联系方式样式 */
.contact-list {
	padding: 0;
}

.contact-item {
	display: flex;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.3s;
}

.contact-item:last-child {
	border-bottom: none;
}

.contact-item:active {
	background-color: #f5f5f5;
}

.contact-icon {
	font-size: 36rpx;
	margin-right: 30rpx;
	width: 40rpx;
	text-align: center;
}

.contact-content {
	flex: 1;
}

.contact-title {
	display: block;
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.contact-value {
	display: block;
	font-size: 28rpx;
	color: #007AFF;
	margin-bottom: 5rpx;
}

.contact-desc {
	display: block;
	font-size: 24rpx;
	color: #666;
}

.contact-arrow {
	font-size: 28rpx;
	color: #ccc;
}

/* 二维码样式 */
.qrcode-container {
	padding: 40rpx;
	text-align: center;
}

.qrcode-image-container {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.qrcode-image {
	width: 300rpx;
	height: 300rpx;
	border-radius: 15rpx;
	border: 1rpx solid #eee;
	margin-bottom: 20rpx;
}

.qrcode-placeholder {
	width: 300rpx;
	height: 300rpx;
	background: #f8f9fa;
	border: 2rpx dashed #ddd;
	border-radius: 15rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin: 0 auto 20rpx;
}

.qrcode-text {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.qrcode-desc {
	font-size: 24rpx;
	color: #999;
}

.qrcode-tip {
	font-size: 22rpx;
	color: #999;
	font-style: italic;
}

/* 快速帮助样式 */
.quick-help-list {
	padding: 0;
}

.help-item {
	display: flex;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.3s;
}

.help-item:last-child {
	border-bottom: none;
}

.help-item:active {
	background-color: #f5f5f5;
}

.help-icon {
	font-size: 36rpx;
	margin-right: 30rpx;
	width: 40rpx;
	text-align: center;
}

.help-content {
	flex: 1;
}

.help-title {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 8rpx;
}

.help-desc {
	display: block;
	font-size: 24rpx;
	color: #666;
}

.help-arrow {
	font-size: 28rpx;
	color: #ccc;
}

/* 服务时间样式 */
.service-time {
	padding: 20rpx 0;
}

.time-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 40rpx;
}

.time-label {
	font-size: 28rpx;
	color: #666;
}

.time-value {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

/* 反馈建议样式 */
.feedback-section {
	background: white;
	margin: 20rpx;
	border-radius: 20rpx;
	padding: 40rpx;
	text-align: center;
}

.feedback-title {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
}

.feedback-desc {
	display: block;
	font-size: 26rpx;
	color: #666;
	margin-bottom: 30rpx;
}

.feedback-btn {
	background: linear-gradient(135deg, #28a745, #20c997);
	color: white;
	border: none;
	border-radius: 50rpx;
	padding: 24rpx 60rpx;
	font-size: 28rpx;
	margin: 0;
}

.feedback-btn-text {
	color: white;
	font-weight: bold;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	background: white;
	border-radius: 20rpx;
	width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.modal-close {
	font-size: 40rpx;
	color: #999;
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-body {
	padding: 30rpx 40rpx;
}

.feedback-textarea {
	width: 100%;
	min-height: 200rpx;
	border: 1rpx solid #ddd;
	border-radius: 10rpx;
	padding: 20rpx;
	font-size: 28rpx;
	line-height: 1.5;
	resize: none;
	box-sizing: border-box;
}

.char-count {
	display: block;
	text-align: right;
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}

.modal-footer {
	display: flex;
	padding: 20rpx 40rpx 30rpx;
	gap: 20rpx;
}

.modal-btn {
	flex: 1;
	padding: 20rpx;
	border-radius: 10rpx;
	font-size: 28rpx;
	border: none;
	margin: 0;
}

.cancel-btn {
	background: #f8f9fa;
	color: #666;
}

.submit-btn {
	background: #007AFF;
	color: white;
}
</style>