<template>
	<view class="category-select-page">
		<!-- 导航栏 -->
		<view class="navbar">
			<view class="nav-back" @click="goBack">
				<text class="back-icon">←</text>
			</view>
			<view class="nav-title">
				<text class="title-text">{{ pageTitle }}</text>
			</view>
			<view class="nav-placeholder"></view>
		</view>

		<!-- 页面内容 -->
		<view class="page-content">
			<!-- 页面说明 -->
			<view class="page-description">
				<text class="description-text">请选择{{ typeText }}停车场分类</text>
			</view>

			<!-- 加载状态 -->
			<view v-if="loading" class="loading-container">
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 分类列表 -->
			<view v-else-if="categories.length > 0" class="categories-list">
				<view
					class="category-item"
					v-for="category in categories"
					:key="category.id"
					@click="selectCategory(category)"
				>
					<view class="category-content">
						<view class="category-info">
							<text class="category-name">{{ category.name }}</text>
							<text class="category-count">{{ category.parking_count || 0 }}个停车场</text>
						</view>
						<text class="category-arrow">></text>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-else class="empty-state">
				<text class="empty-icon">📋</text>
				<text class="empty-text">暂无{{ typeText }}停车场分类</text>
				<text class="empty-desc">请稍后再试或联系客服</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getParkingCategories, getParkingLots } from '../../api/parking.js'

// 响应式数据
const categories = ref([])
const loading = ref(false)
const parkingType = ref('') // 'airport' 或 'railway'

// 计算属性
const typeText = computed(() => {
	return parkingType.value === 'airport' ? '机场' : '高铁站'
})

const pageTitle = computed(() => {
	return `${typeText.value}停车场分类`
})

// 页面加载时获取参数
onLoad((options) => {
	if (options.type) {
		parkingType.value = options.type
	}
	loadCategories()
})

// 获取分类数据
const loadCategories = async () => {
	if (loading.value) return

	loading.value = true
	try {
		const response = await getParkingCategories()

		if (response.success && response.data) {
			// 获取所有分类
			const categoriesData = response.data

			// 为每个分类统计停车场数量
			const categoriesWithCount = await Promise.all(
				categoriesData.map(async (category) => {
					try {
						// 获取该分类下的停车场数量
						const parkingResponse = await getParkingLots({
							categoryId: category.id,
							limit: 1, // 只需要获取总数，不需要具体数据
							page: 1
						})

						const parkingCount = parkingResponse.success && parkingResponse.data
							? (parkingResponse.data.total || 0)
							: 0

						return {
							...category,
							parking_count: parkingCount
						}
					} catch (error) {
						console.warn(`获取分类 ${category.name} 的停车场数量失败:`, error)
						return {
							...category,
							parking_count: 0
						}
					}
				})
			)

			categories.value = categoriesWithCount
		} else {
			console.warn('获取分类数据失败:', response.message)
			categories.value = []
		}
	} catch (error) {
		console.error('获取分类数据错误:', error)
		uni.showToast({
			title: '获取数据失败',
			icon: 'none',
			duration: 2000
		})
	} finally {
		loading.value = false
	}
}

// 选择分类
const selectCategory = (category) => {
	// 由于列表页是tabBar页面，需要使用switchTab，但switchTab不支持传参
	// 所以我们先将参数存储到全局状态或本地存储中
	const params = {
		categoryId: category.id,
		type: parkingType.value,
		categoryName: category.name
	}

	// 将参数存储到本地存储
	uni.setStorageSync('listPageParams', params)

	// 跳转到列表页
	uni.switchTab({
		url: '/pages/list/list',
		success: () => {
			console.log('成功跳转到列表页，参数已存储:', params)
		},
		fail: (error) => {
			console.error('跳转到列表页失败:', error)
			// 如果switchTab失败，尝试使用navigateTo
			uni.navigateTo({
				url: `/pages/list/list?categoryId=${category.id}&type=${parkingType.value}&categoryName=${encodeURIComponent(category.name)}`
			})
		}
	})
}

// 返回上一页
const goBack = () => {
	uni.navigateBack()
}

// 生命周期
onMounted(() => {
	console.log('分类选择页面加载完成')
})
</script>

<style scoped>
.category-select-page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 导航栏样式 */
.navbar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 88rpx;
	background: white;
	padding: 0 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	position: sticky;
	top: 0;
	z-index: 100;
}

.nav-back {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.back-icon {
	font-size: 36rpx;
	color: #333;
	font-weight: bold;
}

.nav-title {
	flex: 1;
	display: flex;
	justify-content: center;
}

.title-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.nav-placeholder {
	width: 60rpx;
}

/* 页面内容样式 */
.page-content {
	padding: 30rpx 20rpx;
}

.page-description {
	text-align: center;
	margin-bottom: 40rpx;
}

.description-text {
	font-size: 28rpx;
	color: #666;
}

/* 加载状态样式 */
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 400rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #999;
}

/* 分类列表样式 */
.categories-list {
	background: white;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.category-item {
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.2s ease;
}

.category-item:last-child {
	border-bottom: none;
}

.category-item:active {
	background-color: #f8f9fa;
}

.category-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 40rpx;
}

.category-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.category-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.category-count {
	font-size: 26rpx;
	color: #666;
}

.category-arrow {
	font-size: 32rpx;
	color: #ccc;
	font-weight: bold;
}

/* 空状态样式 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 400rpx;
	text-align: center;
}

.empty-icon {
	font-size: 80rpx;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.empty-desc {
	font-size: 24rpx;
	color: #999;
}
</style>
