<template>
	<view class="container">
		<!-- 公司介绍 -->
		<view class="section">
			<view class="section-header">
				<view class="section-icon">🏢</view>
				<text class="section-title">关于我们</text>
			</view>
			<view class="company-info">
				<text class="company-name">共享停车平台</text>
				<text class="company-desc">致力于为用户提供便捷、高效的停车服务，通过智能化管理让停车更简单</text>
			</view>
		</view>

		<!-- 服务特色 -->
		<view class="section">
			<view class="section-header">
				<view class="section-icon">⭐</view>
				<text class="section-title">服务特色</text>
			</view>
			<view class="features-grid">
				<view class="feature-item">
					<view class="feature-icon">📍</view>
					<text class="feature-title">精准定位</text>
					<text class="feature-desc">实时定位附近停车场</text>
				</view>
				<view class="feature-item">
					<view class="feature-icon">💰</view>
					<text class="feature-title">价格透明</text>
					<text class="feature-desc">明码标价，无隐藏费用</text>
				</view>
				<view class="feature-item">
					<view class="feature-icon">⚡</view>
					<text class="feature-title">快速预定</text>
					<text class="feature-desc">一键预定，省时省心</text>
				</view>
				<view class="feature-item">
					<view class="feature-icon">🎫</view>
					<text class="feature-title">优惠多多</text>
					<text class="feature-desc">丰富优惠券，停车更省钱</text>
				</view>
			</view>
		</view>

		<!-- 功能菜单 -->
		<view class="section">
			<view class="section-header">
				<view class="section-icon">📋</view>
				<text class="section-title">服务指南</text>
			</view>
			<view class="menu-list">
				<view class="menu-item" @click="navigateTo('/pages/service-guide/service-guide')">
					<view class="menu-icon">📖</view>
					<view class="menu-content">
						<text class="menu-title">服务流程介绍</text>
						<text class="menu-desc">了解如何使用共享停车服务</text>
					</view>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item" @click="navigateTo('/pages/service/service')">
					<view class="menu-icon">📞</view>
					<view class="menu-content">
						<text class="menu-title">联系客服</text>
						<text class="menu-desc">遇到问题？联系我们的客服团队</text>
					</view>
					<text class="menu-arrow">></text>
				</view>
			</view>
		</view>

		<!-- 版本信息 -->
		<view class="section">
			<view class="section-header">
				<view class="section-icon">ℹ️</view>
				<text class="section-title">版本信息</text>
			</view>
			<view class="version-info">
				<view class="version-item">
					<text class="version-label">当前版本</text>
					<text class="version-value">v1.0.0</text>
				</view>
				<view class="version-item">
					<text class="version-label">更新时间</text>
					<text class="version-value">2025-07-18</text>
				</view>
			</view>
		</view>

		<!-- 底部信息 -->
		<view class="footer">
			<text class="footer-text">© 2025 共享停车平台</text>
			<text class="footer-text">让停车更简单</text>
		</view>
	</view>
</template>

<script setup>
// 页面导航
const navigateTo = (url) => {
	uni.navigateTo({
		url,
		fail: (error) => {
			console.error('页面跳转失败:', error)
			uni.showToast({
				title: '页面暂未开放',
				icon: 'none',
				duration: 2000
			})
		}
	})
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding: 20rpx;
}

.section {
	background: white;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
}

.section-header {
	display: flex;
	align-items: center;
	padding: 30rpx 40rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.section-icon {
	font-size: 36rpx;
	margin-right: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

/* 公司介绍样式 */
.company-info {
	padding: 40rpx;
	text-align: center;
}

.company-name {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #007AFF;
	margin-bottom: 20rpx;
}

.company-desc {
	display: block;
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

/* 服务特色样式 */
.features-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
	padding: 30rpx;
}

.feature-item {
	text-align: center;
	padding: 30rpx 20rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
}

.feature-icon {
	font-size: 40rpx;
	margin-bottom: 15rpx;
}

.feature-title {
	display: block;
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.feature-desc {
	display: block;
	font-size: 24rpx;
	color: #666;
}

/* 菜单列表样式 */
.menu-list {
	padding: 0;
}

.menu-item {
	display: flex;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.3s;
}

.menu-item:last-child {
	border-bottom: none;
}

.menu-item:active {
	background-color: #f5f5f5;
}

.menu-icon {
	font-size: 36rpx;
	margin-right: 30rpx;
	width: 40rpx;
	text-align: center;
}

.menu-content {
	flex: 1;
}

.menu-title {
	display: block;
	font-size: 30rpx;
	color: #333;
	margin-bottom: 8rpx;
}

.menu-desc {
	display: block;
	font-size: 24rpx;
	color: #666;
}

.menu-arrow {
	font-size: 28rpx;
	color: #ccc;
}

/* 版本信息样式 */
.version-info {
	padding: 30rpx 40rpx;
}

.version-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15rpx 0;
}

.version-label {
	font-size: 28rpx;
	color: #666;
}

.version-value {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

/* 底部信息样式 */
.footer {
	text-align: center;
	padding: 60rpx 40rpx;
	margin-top: 40rpx;
}

.footer-text {
	display: block;
	font-size: 24rpx;
	color: #999;
	margin-bottom: 10rpx;
}
</style>