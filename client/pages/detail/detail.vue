<template>
	<view class="container">
		<!-- 图片轮播 -->
		<view class="image-section">
			<swiper
				class="image-swiper"
				:indicator-dots="parkingDetail.image_urls.length > 1"
				:autoplay="false"
				indicator-color="rgba(255, 255, 255, 0.5)"
				indicator-active-color="#007AFF"
			>
				<swiper-item v-for="(image, index) in parkingDetail.image_urls" :key="index">
					<image
						class="detail-image"
						:src="image"
						mode="aspectFill"
						@error="handleImageError"
						@click="previewImage(image, index)"
					></image>
				</swiper-item>
			</swiper>

			<!-- 图片数量指示器 -->
			<view class="image-count" v-if="parkingDetail.image_urls.length > 1">
				<text class="count-text">{{ currentImageIndex + 1 }}/{{ parkingDetail.image_urls.length }}</text>
			</view>
		</view>

		<!-- 基本信息 -->
		<view class="info-section">
			<view class="basic-info">
				<text class="parking-name">{{ parkingDetail.name }}</text>
				<text class="parking-address address-text" @longpress="copyAddress">{{ parkingDetail.address }}</text>
				<text class="copy-tip">长按复制地址</text>
				<view class="price-info">
					<text class="price-label">停车费用：</text>
					<text class="price-value">{{ getPriceRange() }}</text>
				</view>
				<view class="contact-info" v-if="parkingDetail.contact_phone">
					<text class="contact-label">联系电话：</text>
					<text class="contact-value" @click="makePhoneCall">{{ parkingDetail.contact_phone }}</text>
				</view>
			</view>
		</view>

		<!-- 收费标准 -->
		<view class="pricing-section">
			<view class="section-title">收费标准</view>

			<!-- 新的累计天数收费模式 -->
			<view class="cumulative-pricing" v-if="isCumulativePricing()">
				<view class="pricing-mode-title">
					<text class="mode-icon">📅</text>
					<text class="mode-text">累计天数收费模式</text>
				</view>
				
				<!-- 前7天价格表 -->
				<view class="daily-pricing-table">
					<view class="table-header">
						<text class="header-cell day">天数</text>
						<text class="header-cell cumulative">累计费用</text>
					</view>
					<view
						class="table-row"
						v-for="day in 7"
						:key="day"
					>
						<text class="table-cell day">第{{ day }}天</text>
						<text class="table-cell cumulative">¥{{ getDayPrice(day) }}</text>
					</view>
				</view>

				<!-- 第8天起费用 -->
				<view class="after-7-pricing">
					<view class="after-7-item">
						<text class="after-7-label">第8天起每天：</text>
						<text class="after-7-price">¥{{ getAfter7Price() }}</text>
					</view>
				</view>

				<!-- 计费说明 -->
				<view class="pricing-tips">
					<view class="tips-item">
						<text class="tip-icon">💡</text>
						<text class="tip-text">停车时长不足1天按1天计算</text>
					</view>
					<view class="tips-item">
						<text class="tip-icon">📊</text>
						<text class="tip-text">采用累计价格，长期停车更优惠</text>
					</view>
				</view>
			</view>

			<!-- 兼容旧的小时费率价格规则 -->
			<view class="hourly-pricing" v-else-if="parkingDetail.price_rules && parkingDetail.price_rules.length > 0">
				<view class="pricing-mode-title">
					<text class="mode-icon">⏰</text>
					<text class="mode-text">小时费率收费模式</text>
				</view>
				
				<view class="pricing-table">
					<view class="table-header">
						<text class="header-cell rule-name">时段名称</text>
						<text class="header-cell time-range">时间段</text>
						<text class="header-cell price">价格</text>
					</view>
					<view
						class="table-row"
						v-for="(rule, index) in parkingDetail.price_rules"
						:key="index"
					>
						<text class="table-cell rule-name">{{ rule.rule_name }}</text>
						<text class="table-cell time-range">{{ rule.start_time }}-{{ rule.end_time }}</text>
						<text class="table-cell price">¥{{ rule.price_per_hour }}/小时</text>
					</view>
				</view>
			</view>

			<!-- 默认收费说明 -->
			<view class="default-pricing" v-else>
				<view class="pricing-content">
					<text class="pricing-text">价格面议，请联系停车场了解详情</text>
				</view>
			</view>

			<!-- 收费说明 -->
			<view class="pricing-note">
				<text class="note-text">* 具体收费以实际停车时长为准</text>
			</view>
		</view>

		<!-- 服务设施 -->
		<view class="facilities-section">
			<view class="section-title">服务设施</view>
			<view class="facilities-grid">
				<view
					class="facility-item"
					v-for="(facility, index) in getFacilitiesDisplay()"
					:key="index"
					:class="{ 'available': facility.available }"
				>
					<text class="facility-icon">{{ facility.icon }}</text>
					<text class="facility-name">{{ facility.name }}</text>
					<text class="facility-status" v-if="facility.available">✓</text>
					<text class="facility-status unavailable" v-else>✗</text>
				</view>
			</view>
		</view>

		<!-- 用户评价 -->
		<view class="reviews-section" v-if="parkingDetail.reviews && parkingDetail.reviews.length > 0">
			<view class="section-title">用户评价</view>
			
			<!-- 评价统计 -->
			<view class="review-stats" v-if="parkingDetail.review_stats">
				<view class="stats-summary">
					<view class="average-rating">
						<text class="rating-number">{{ parkingDetail.review_stats.average_rating }}</text>
						<view class="rating-stars">
							<text v-for="i in 5" :key="i" 
								  class="star" 
								  :class="{ 'star-filled': i <= Math.round(parkingDetail.review_stats.average_rating) }">
								★
							</text>
						</view>
						<text class="total-reviews">{{ parkingDetail.review_stats.total_reviews }}条评价</text>
					</view>
					
					<view class="rating-bars">
						<view v-for="(count, star) in getRatingDistribution()" :key="star" class="rating-bar">
							<text class="star-label">{{ star }}星</text>
							<view class="bar-container">
								<view class="bar-fill" :style="{ width: getBarWidth(count) }"></view>
							</view>
							<text class="count-text">{{ count }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 评价列表 -->
			<view class="reviews-list">
				<view v-for="review in parkingDetail.reviews" :key="review.id" class="review-item">
					<view class="review-header">
						<view class="user-info">
							<image 
								class="user-avatar" 
								:src="review.user_avatar || '/static/default-avatar.png'"
								mode="aspectFill"
							/>
							<view class="user-details">
								<text class="user-name">{{ review.user_nickname || '匿名用户' }}</text>
								<text class="review-time">{{ formatReviewDate(review.created_at) }}</text>
							</view>
						</view>
						
						<view class="review-rating">
							<view class="stars">
								<text v-for="i in 5" :key="i" 
									  class="star small" 
									  :class="{ 'star-filled': i <= review.rating }">
									★
								</text>
							</view>
						</view>
					</view>

					<view class="review-content">
						<text class="comment-text">{{ review.comment || '用户未留言，但给出了评分' }}</text>
					</view>

					<!-- 管理员回复 -->
					<view v-if="review.replies && review.replies.length > 0" class="replies-section">
						<view v-for="reply in review.replies" :key="reply.id" class="reply-item">
							<view class="reply-header">
								<text class="reply-author">{{ reply.reply_user_nickname || '管理员' }}回复：</text>
								<text class="reply-time">{{ formatReviewDate(reply.created_at) }}</text>
							</view>
							<text class="reply-content">{{ reply.content }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 无评价提示 -->
		<view class="no-reviews-section" v-else>
			<view class="section-title">用户评价</view>
			<view class="no-reviews-content">
				<text class="no-reviews-icon">💬</text>
				<text class="no-reviews-text">暂无用户评价</text>
				<text class="no-reviews-tip">成为第一个评价的用户吧！</text>
			</view>
		</view>

		<!-- 底部操作按钮 -->
		<view class="bottom-actions">
			<button class="action-btn booking-btn" @click="handleBooking">
				<text class="btn-text">立即预定</text>
			</button>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getParkingLotById } from '../../api/parking.js'
import { getFullImageUrl } from '../../utils/request.js'

// 响应式数据
const parkingDetail = ref({
	id: '',
	name: '',
	address: '',
	contact_phone: '',
	latitude: 0,
	longitude: 0,
	total_spaces: 0,
	description: '',
	image_urls: ['/static/parking-default.svg'],
	price_rules: [],
	service_facilities: [],
	reviews: [],
	review_stats: null
})

const loading = ref(false)
const currentImageIndex = ref(0)

// 获取停车场详情
const loadParkingDetail = async (id: string) => {
	if (loading.value) return

	loading.value = true
	try {
		const response = await getParkingLotById(id)
		if (response.success) {
			const data = response.data

			// 处理图片数据
			let imageUrls = ['/static/parking-default.svg']
			if (data.image_urls && Array.isArray(data.image_urls) && data.image_urls.length > 0) {
				// 将相对路径转换为完整URL
				imageUrls = data.image_urls.map(url => getFullImageUrl(url))
			}

			// 处理价格规则数据
			let priceRules = []
			if (data.price_rules) {
				// 如果是字符串，尝试解析为JSON
				if (typeof data.price_rules === 'string') {
					try {
						priceRules = JSON.parse(data.price_rules)
					} catch (error) {
						console.warn('解析价格规则失败:', error)
						priceRules = []
					}
				} else {
					// 直接使用对象或数组
					priceRules = data.price_rules
				}
			}

			// 处理服务设施数据
			let serviceFacilities = []
			if (data.service_facilities && Array.isArray(data.service_facilities)) {
				serviceFacilities = data.service_facilities
			}

			parkingDetail.value = {
				...data,
				image_urls: imageUrls,
				price_rules: priceRules,
				service_facilities: serviceFacilities
			}


		}
	} catch (error) {
		console.error('获取停车场详情失败:', error)
		uni.showToast({
			title: '获取详情失败',
			icon: 'none',
			duration: 2000
		})
	} finally {
		loading.value = false
	}
}



// 立即预定
const handleBooking = () => {
	// 检查登录状态
	const token = uni.getStorageSync('token')
	if (!token) {
		uni.showModal({
			title: '提示',
			content: '请先登录后再进行预定',
			success: (res: any) => {
				if (res.confirm) {
					uni.switchTab({
						url: '/pages/my/my'
					})
				}
			}
		})
		return
	}

	// 跳转到预定页面
	uni.navigateTo({
		url: `/pages/booking/booking?parkingLotId=${parkingDetail.value.id}`
	})
}

// 检查是否为累计天数收费模式
const isCumulativePricing = () => {
	return parkingDetail.value.price_rules && 
		   typeof parkingDetail.value.price_rules === 'object' && 
		   parkingDetail.value.price_rules.type === 'cumulative_daily'
}

// 获取指定天数的累计价格
const getDayPrice = (day: number) => {
	if (!isCumulativePricing()) return '0.00'
	
	const dailyPrices = parkingDetail.value.price_rules.daily_prices
	if (!dailyPrices) return '0.00'
	
	const dayKey = `day_${day}`
	const price = dailyPrices[dayKey] || 0
	return Number(price).toFixed(2)
}

// 获取第8天起每日价格
const getAfter7Price = () => {
	if (!isCumulativePricing()) return '0.00'
	
	const price = parkingDetail.value.price_rules.daily_price_after_7 || 20
	return Number(price).toFixed(2)
}

// 获取价格范围显示
const getPriceRange = () => {
	// 检查是否有新的累计天数价格配置
	if (isCumulativePricing()) {
		const dailyPrices = parkingDetail.value.price_rules.daily_prices
		if (dailyPrices) {
			const day1Price = dailyPrices.day_1 || 0
			const day7Price = dailyPrices.day_7 || 0
			
			if (day1Price === 0) {
				return `首日免费，7天¥${day7Price}`
			} else if (day1Price === day7Price) {
				return `¥${day1Price}/天`
			} else {
				return `¥${day1Price}-${day7Price}/天`
			}
		}
	}

	// 兼容旧的小时费率价格规则
	if (parkingDetail.value.price_rules && Array.isArray(parkingDetail.value.price_rules) && parkingDetail.value.price_rules.length > 0) {
		const prices = parkingDetail.value.price_rules.map((rule: any) => Number(rule.price_per_hour) || 0)
		const minPrice = Math.min(...prices)
		const maxPrice = Math.max(...prices)

		if (minPrice === maxPrice) {
			return `¥${minPrice}/小时`
		} else {
			return `¥${minPrice}-${maxPrice}/小时`
		}
	}
	return '价格面议'
}

// 获取服务设施显示
const getFacilitiesDisplay = () => {
	const allFacilities = [
		{ key: 'has_charging_pile', name: '充电桩', icon: '🔌' },
		{ key: 'is_24_hours', name: '24小时开放', icon: '🕐' },
		{ key: 'has_camera', name: '监控摄像头', icon: '📹' },
		{ key: 'is_indoor', name: '室内停车', icon: '🏢' }
	]

	return allFacilities.map(facility => ({
		...facility,
		available: parkingDetail.value.service_facilities.includes(facility.key)
	}))
}

// 处理图片加载错误
const handleImageError = (e: any) => {
	console.log('图片加载失败:', e)
	e.target.src = '/static/parking-default.svg'
}

// 预览图片
const previewImage = (current: string, index: number) => {
	currentImageIndex.value = index
	uni.previewImage({
		urls: parkingDetail.value.image_urls,
		current: current
	})
}

// 复制地址到剪贴板
const copyAddress = () => {
	if (!parkingDetail.value?.address) return
	
	uni.setClipboardData({
		data: parkingDetail.value.address,
		success: () => {
			uni.showToast({
				title: '地址已复制',
				icon: 'success'
			})
		},
		fail: () => {
			uni.showToast({
				title: '复制失败',
				icon: 'none'
			})
		}
	})
}

// 拨打电话
const makePhoneCall = () => {
	if (parkingDetail.value.contact_phone) {
		uni.makePhoneCall({
			phoneNumber: parkingDetail.value.contact_phone
		})
	}
}

// 格式化评价日期
const formatReviewDate = (dateString: string) => {
	if (!dateString) return ''
	const date = new Date(dateString)
	const now = new Date()
	const diff = now.getTime() - date.getTime()
	const days = Math.floor(diff / (1000 * 60 * 60 * 24))
	
	if (days === 0) {
		return '今天'
	} else if (days === 1) {
		return '昨天'
	} else if (days < 7) {
		return `${days}天前`
	} else {
		const year = date.getFullYear()
		const month = String(date.getMonth() + 1).padStart(2, '0')
		const day = String(date.getDate()).padStart(2, '0')
		return `${year}-${month}-${day}`
	}
}

// 获取评分分布
const getRatingDistribution = () => {
	if (!parkingDetail.value.review_stats?.rating_distribution) {
		return { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
	}
	
	const dist = parkingDetail.value.review_stats.rating_distribution
	return {
		5: dist.five_star || 0,
		4: dist.four_star || 0,
		3: dist.three_star || 0,
		2: dist.two_star || 0,
		1: dist.one_star || 0
	}
}

// 获取评分条宽度
const getBarWidth = (count: number) => {
	const total = parkingDetail.value.review_stats?.total_reviews || 1
	const percentage = (count / total) * 100
	return `${Math.max(percentage, 2)}%` // 最小宽度2%，确保可见
}

// 页面加载
onLoad((options: any) => {
	const id = options?.id
	if (id) {
		loadParkingDetail(id)
	} else {
		uni.showToast({
			title: '参数错误',
			icon: 'none',
			duration: 2000
		})
		setTimeout(() => {
			uni.navigateBack()
		}, 2000)
	}
})

// 生命周期
onMounted(() => {
	console.log('详情页加载完成')
})
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 图片轮播样式 */
.image-section {
	background: white;
	position: relative;
}

.image-swiper {
	height: 400rpx;
}

.detail-image {
	width: 100%;
	height: 100%;
}

.image-count {
	position: absolute;
	bottom: 20rpx;
	right: 20rpx;
	background: rgba(0, 0, 0, 0.6);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.count-text {
	color: white;
	font-size: 24rpx;
}

/* 基本信息样式 */
.info-section {
	background: white;
	margin-top: 20rpx;
	padding: 30rpx;
}

.basic-info {
	display: flex;
	flex-direction: column;
}

.parking-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
}

.parking-address {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 5rpx;
}

.address-text {
	color: #007aff !important;
	cursor: pointer;
}

.copy-tip {
	font-size: 22rpx;
	color: #999;
	margin-bottom: 20rpx;
}

.price-info {
	display: flex;
	align-items: center;
}

.price-label {
	font-size: 28rpx;
	color: #333;
}

.price-value {
	font-size: 32rpx;
	color: #007AFF;
	font-weight: bold;
	margin-left: 10rpx;
}

.contact-info {
	display: flex;
	align-items: center;
	margin-top: 15rpx;
}

.contact-label {
	font-size: 28rpx;
	color: #333;
}

.contact-value {
	font-size: 28rpx;
	color: #007AFF;
	margin-left: 10rpx;
	text-decoration: underline;
}

/* 收费标准样式 */
.pricing-section {
	background: white;
	margin-top: 20rpx;
	padding: 30rpx;
}

/* 价格表格样式 */
.pricing-table {
	border: 1rpx solid #e8e8e8;
	border-radius: 8rpx;
	overflow: hidden;
	margin-bottom: 20rpx;
}

.table-header {
	display: flex;
	background: #f5f5f5;
	border-bottom: 1rpx solid #e8e8e8;
}

.table-row {
	display: flex;
	border-bottom: 1rpx solid #e8e8e8;
}

.table-row:last-child {
	border-bottom: none;
}

.header-cell,
.table-cell {
	padding: 20rpx 10rpx;
	text-align: center;
	font-size: 26rpx;
}

.header-cell {
	font-weight: bold;
	color: #333;
}

.table-cell {
	color: #666;
}

.rule-name {
	flex: 2;
	text-align: left;
}

.time-range {
	flex: 2;
}

.price {
	flex: 1.5;
	color: #007AFF;
	font-weight: bold;
}

.pricing-note {
	margin-top: 15rpx;
}

.note-text {
	font-size: 24rpx;
	color: #999;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.pricing-content {
	background: #f8f9fa;
	padding: 20rpx;
	border-radius: 10rpx;
}

.pricing-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

/* 服务设施样式 */
.facilities-section {
	background: white;
	margin-top: 20rpx;
	padding: 30rpx;
}

.facilities-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.facility-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	border-radius: 10rpx;
	background: #f8f9fa;
	min-width: 120rpx;
	position: relative;
	border: 2rpx solid transparent;
}

.facility-item.available {
	background: #e6f7ff;
	border-color: #91d5ff;
}

.facility-icon {
	font-size: 32rpx;
	margin-bottom: 8rpx;
}

.facility-name {
	font-size: 24rpx;
	color: #333;
	text-align: center;
	margin-bottom: 5rpx;
}

.facility-status {
	font-size: 20rpx;
	font-weight: bold;
	color: #52c41a;
}

.facility-status.unavailable {
	color: #ff4d4f;
}



/* 底部操作按钮样式 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 20rpx;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	border: none;
	font-size: 32rpx;
	font-weight: bold;
}

.navigation-btn {
	background: #f8f9fa;
	color: #333;
}

.booking-btn {
	background: linear-gradient(135deg, #007AFF, #5AC8FA);
	color: white;
}

.btn-text {
	color: inherit;
}

/* 累计天数收费模式样式 */
.cumulative-pricing {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
}

.pricing-mode-title {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	padding-bottom: 16rpx;
	border-bottom: 1rpx solid #e8e8e8;
}

.mode-icon {
	font-size: 28rpx;
	margin-right: 12rpx;
}

.mode-text {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

/* 每日价格表格 */
.daily-pricing-table {
	background: white;
	border-radius: 8rpx;
	overflow: hidden;
	margin-bottom: 20rpx;
	border: 1rpx solid #e8e8e8;
}

.daily-pricing-table .table-header {
	background: #007AFF;
}

.daily-pricing-table .header-cell {
	color: white;
	font-weight: bold;
}

.daily-pricing-table .header-cell.day {
	flex: 1;
}

.daily-pricing-table .header-cell.cumulative {
	flex: 1.5;
}

.daily-pricing-table .table-cell.day {
	flex: 1;
	text-align: left;
	padding-left: 20rpx;
}

.daily-pricing-table .table-cell.cumulative {
	flex: 1.5;
	color: #007AFF;
	font-weight: bold;
}

/* 第8天起费用 */
.after-7-pricing {
	background: white;
	border-radius: 8rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	border: 1rpx solid #e8e8e8;
}

.after-7-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.after-7-label {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.after-7-price {
	font-size: 32rpx;
	color: #ff6b35;
	font-weight: bold;
}

/* 计费说明 */
.pricing-tips {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.tips-item {
	display: flex;
	align-items: center;
}

.tip-icon {
	font-size: 24rpx;
	margin-right: 12rpx;
}

.tip-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
}

/* 小时费率收费模式样式 */
.hourly-pricing {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
}

/* 默认收费样式 */
.default-pricing {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
}

/* 评价部分样式 */
.reviews-section {
	background: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.no-reviews-section {
	background: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.no-reviews-content {
	text-align: center;
	padding: 60rpx 20rpx;
}

.no-reviews-icon {
	font-size: 60rpx;
	display: block;
	margin-bottom: 20rpx;
}

.no-reviews-text {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.no-reviews-tip {
	font-size: 24rpx;
	color: #999;
}

/* 评价统计样式 */
.review-stats {
	margin-bottom: 30rpx;
	padding-bottom: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.stats-summary {
	display: flex;
	gap: 40rpx;
}

.average-rating {
	display: flex;
	flex-direction: column;
	align-items: center;
	min-width: 120rpx;
}

.rating-number {
	font-size: 48rpx;
	font-weight: bold;
	color: #ff6b35;
	line-height: 1;
	margin-bottom: 8rpx;
}

.rating-stars {
	display: flex;
	margin-bottom: 8rpx;
}

.star {
	font-size: 24rpx;
	color: #ddd;
	margin-right: 4rpx;
}

.star.small {
	font-size: 20rpx;
}

.star-filled {
	color: #ffa500;
}

.total-reviews {
	font-size: 22rpx;
	color: #999;
}

.rating-bars {
	flex: 1;
}

.rating-bar {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
	gap: 12rpx;
}

.star-label {
	font-size: 22rpx;
	color: #666;
	width: 40rpx;
}

.bar-container {
	flex: 1;
	height: 12rpx;
	background-color: #f0f0f0;
	border-radius: 6rpx;
	overflow: hidden;
}

.bar-fill {
	height: 100%;
	background-color: #ffa500;
	border-radius: 6rpx;
	transition: width 0.3s ease;
}

.count-text {
	font-size: 22rpx;
	color: #999;
	width: 30rpx;
	text-align: right;
}

/* 评价列表样式 */
.reviews-list {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

.review-item {
	padding: 24rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
}

.review-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 16rpx;
}

.user-info {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.user-avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
}

.user-details {
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.user-name {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.review-time {
	font-size: 22rpx;
	color: #999;
}

.review-rating {
	display: flex;
	align-items: center;
}

.review-content {
	margin-bottom: 16rpx;
}

.comment-text {
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
}

/* 回复样式 */
.replies-section {
	background-color: #fff;
	border-radius: 8rpx;
	padding: 20rpx;
	margin-top: 16rpx;
	border-left: 4rpx solid #007aff;
}

.reply-item {
	margin-bottom: 16rpx;
}

.reply-item:last-child {
	margin-bottom: 0;
}

.reply-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8rpx;
}

.reply-author {
	font-size: 24rpx;
	color: #007aff;
	font-weight: 500;
}

.reply-time {
	font-size: 22rpx;
	color: #999;
}

.reply-content {
	font-size: 26rpx;
	color: #333;
	line-height: 1.5;
}
</style>
