<template>
	<view class="container">
		<!-- 加载状态 -->
		<view v-if="isLoading" class="loading-section">
			<view class="loading-card">
				<view class="loading-spinner"></view>
				<text class="loading-text">正在加载用户信息...</text>
			</view>
		</view>

		<!-- 未登录状态 -->
		<view v-else-if="!isLoggedIn" class="login-section">
			<view class="login-card">
				<image class="login-icon" src="/static/logo.png" mode="aspectFit"></image>
				<text class="login-title">欢迎使用共享停车</text>
				<text class="login-subtitle">请先登录以享受完整服务</text>
				<button class="login-btn" @click="handleWxLogin">
					<text class="login-btn-text">微信一键登录</text>
				</button>
			</view>
		</view>

		<!-- 已登录状态 -->
		<view v-else class="profile-section">
			<!-- 用户信息头部 -->
			<view class="user-header">
				<image class="avatar" :src="userInfo.avatar || '/static/default-avatar.png'" mode="aspectFill"></image>
				<view class="user-info">
					<text class="nickname">{{ userInfo.nickname || '用户' }}</text>
					<text class="phone">{{ userInfo.phone || '未绑定手机号' }}</text>
				</view>
			</view>

			<!-- 功能菜单 -->
			<view class="menu-section">
				<!-- 停车场管理员专用菜单 -->
				<view v-if="isParkingOperator" class="menu-group">
					<view class="menu-item operator-item" @click="navigateTo('/pages/parking-operator/dashboard')">
						<view class="menu-icon">🏢</view>
						<text class="menu-text">停车场管理</text>
						<text class="menu-arrow">></text>
					</view>
					<view class="menu-item operator-item" @click="navigateTo('/pages/parking-operator/orders')">
						<view class="menu-icon">📋</view>
						<text class="menu-text">订单管理</text>
						<text class="menu-arrow">></text>
					</view>
				</view>

				<!-- 普通用户菜单 -->
				<view class="menu-group">
					<view class="menu-item" @click="navigateTo('/pages/vehicles/vehicles')">
						<view class="menu-icon">🚗</view>
						<text class="menu-text">我的车辆</text>
						<text class="menu-arrow">></text>
					</view>
					<view class="menu-item" @click="navigateTo('/pages/coupons/coupons')">
						<view class="menu-icon">🎫</view>
						<text class="menu-text">我的优惠券</text>
						<text class="menu-arrow">></text>
					</view>
					<view class="menu-item" @click="navigateTo('/pages/coupon-center/coupon-center')">
						<view class="menu-icon">🎁</view>
						<text class="menu-text">领券中心</text>
						<text class="menu-arrow">></text>
					</view>
				</view>

				<view class="menu-group">
					<view class="menu-item" @click="navigateTo('/pages/service/service')">
						<view class="menu-icon">📞</view>
						<text class="menu-text">联系客服</text>
						<text class="menu-arrow">></text>
					</view>
					<view class="menu-item" @click="navigateTo('/pages/about/about')">
						<view class="menu-icon">ℹ️</view>
						<text class="menu-text">关于我们</text>
						<text class="menu-arrow">></text>
					</view>
					<!-- 开发环境测试按钮 -->
					<view class="menu-item dev-test-item" @click="testDevToken" v-if="isDevelopment">
						<view class="menu-icon">🔧</view>
						<text class="menu-text">测试Token状态</text>
						<text class="menu-arrow">></text>
					</view>
				</view>

				<view class="menu-group">
					<view class="menu-item logout-item" @click="handleLogout">
						<view class="menu-icon">🚪</view>
						<text class="menu-text logout-text">退出登录</text>
						<text class="menu-arrow">></text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { wxLogin } from '../../api/user.js'
import roleManager from '../../utils/roleManager.js'
import { get } from '../../utils/request.js'

// 响应式数据
const isLoggedIn = ref(false)
const userInfo = ref({
	nickname: '',
	avatar: '',
	phone: ''
})
const userRole = ref(null)
const isParkingOperator = ref(false)
const isLoading = ref(true) // 添加加载状态

// 开发环境检测
const isDevelopment = ref(false)

// 检测是否为开发环境
const checkDevelopmentMode = () => {
	// #ifdef MP-WEIXIN
	try {
		const accountInfo = uni.getAccountInfoSync()
		isDevelopment.value = accountInfo.miniProgram.envVersion === 'develop' ||
			accountInfo.miniProgram.envVersion === 'trial'
	} catch (error) {
		isDevelopment.value = true // 获取失败时默认为开发环境
	}
	// #endif

	// #ifndef MP-WEIXIN
	isDevelopment.value = true // 非微信环境默认为开发环境
	// #endif
}

// 检查登录状态
const checkLoginStatus = async () => {
	try {
		isLoading.value = true // 开始加载

		const token = uni.getStorageSync('token')
		const storedUserInfo = uni.getStorageSync('userInfo')

		if (token && storedUserInfo) {
			// 先设置基本用户信息
			try {
				userInfo.value = JSON.parse(storedUserInfo)
			} catch (parseError) {
				console.warn('解析用户信息失败:', parseError)
				// 清除损坏的数据
				uni.removeStorageSync('userInfo')
				uni.removeStorageSync('token')
				isLoggedIn.value = false
				return
			}

			// 异步检查用户角色，完成后再设置登录状态
			await checkUserRole()

			// 所有信息加载完成后才设置为已登录
			isLoggedIn.value = true
		} else {
			isLoggedIn.value = false
			isParkingOperator.value = false
			userRole.value = null
		}
	} catch (error) {
		console.error('检查登录状态失败:', error)
		isLoggedIn.value = false
		isParkingOperator.value = false
		userRole.value = null
	} finally {
		isLoading.value = false // 结束加载
	}
}

// 检查用户角色
const checkUserRole = async () => {
	try {
		// 使用初始化方法，优先使用本地缓存
		const roleInfo = await roleManager.initUserRole()
		userRole.value = roleInfo
		isParkingOperator.value = roleInfo.role_type === 'parking_operator'

		console.log('用户角色:', roleInfo.role_type)
		if (isParkingOperator.value) {
			console.log('停车场管理员 - 管理停车场:', roleInfo.parking_lot_name)
		}
	} catch (error) {
		console.error('检查用户角色失败:', error)
		isParkingOperator.value = false
		userRole.value = {
			role_type: 'user',
			permissions: ['view_own_orders', 'create_booking', 'manage_vehicles']
		}
	}
}

// 微信登录处理
const handleWxLogin = async () => {
	try {
		// 显示加载提示
		uni.showLoading({
			title: '登录中...',
			mask: true
		})

		// 清除可能存在的旧token，确保使用新的登录流程
		uni.removeStorageSync('token')
		uni.removeStorageSync('userInfo')

		// 获取微信登录凭证
		const loginRes = await new Promise((resolve, reject) => {
			uni.login({
				provider: 'weixin',
				success: resolve,
				fail: reject
			})
		})

		if (!loginRes.code) {
			throw new Error('获取微信登录凭证失败')
		}

		// 获取用户信息（可选）
		let wxUserInfo = {}
		try {
			const userInfoRes = await new Promise((resolve, reject) => {
				uni.getUserInfo({
					provider: 'weixin',
					success: resolve,
					fail: reject
				})
			})
			wxUserInfo = userInfoRes.userInfo
		} catch (error) {
			console.log('获取用户信息失败，使用默认信息:', error)
		}

		// 调用后端登录接口
		const response = await wxLogin(loginRes.code, wxUserInfo)

		if (response.success) {
			console.log(response.data)
			// 保存登录信息
			uni.setStorageSync('token', response.data.token)
			uni.setStorageSync('userInfo', JSON.stringify(response.data.userInfo))
			
			// 更新页面状态
			isLoggedIn.value = true
			userInfo.value = response.data.userInfo

			uni.showToast({
				title: '登录成功',
				icon: 'success',
				duration: 2000
			})
		} else {
			throw new Error(response.message || '登录失败')
		}
	} catch (error) {
		console.error('微信登录失败:', error)
		uni.showToast({
			title: error.message || '登录失败，请重试',
			icon: 'none',
			duration: 2000
		})
	} finally {
		uni.hideLoading()
	}
}

// 测试开发环境Token状态
const testDevToken = async () => {
	try {
		uni.showLoading({
			title: '测试中...',
			mask: true
		})

		// 检查本地Token
		const token = uni.getStorageSync('token')
		const storedUserInfo = uni.getStorageSync('userInfo')

		console.log('🔍 Token检测结果:')
		console.log('Token存在:', !!token)
		console.log('Token长度:', token ? token.length : 0)
		console.log('用户信息存在:', !!storedUserInfo)

		if (token) {
			console.log('Token前50字符:', token.substring(0, 50) + '...')
		}

		if (storedUserInfo) {
			console.log('用户信息:', JSON.parse(storedUserInfo))
		}

		// 测试一个需要认证的接口（使用管理员profile接口）
		try {
			const response = await get('/api/admin/profile')
			console.log('✅ API测试成功:', response)

			uni.showModal({
				title: 'Token测试结果',
				content: `Token状态: 正常\n用户ID: ${response.data.id}\n用户名: ${response.data.username}\n超级管理员: ${response.data.is_super_admin ? '是' : '否'}\nAPI调用: 成功`,
				showCancel: false
			})
		} catch (apiError) {
			console.error('❌ API测试失败:', apiError)

			uni.showModal({
				title: 'Token测试结果',
				content: `Token状态: ${token ? '存在但可能无效' : '不存在'}\nAPI调用: 失败\n错误: ${apiError.message}`,
				showCancel: false
			})
		}

	} catch (error) {
		console.error('Token测试出错:', error)
		uni.showToast({
			title: '测试失败',
			icon: 'error',
			duration: 2000
		})
	} finally {
		uni.hideLoading()
	}
}

// 退出登录
const handleLogout = () => {
	uni.showModal({
		title: '提示',
		content: '确定要退出登录吗？',
		success: (res: any) => {
			if (res.confirm) {
				// 清除本地存储
				uni.removeStorageSync('token')
				uni.removeStorageSync('userInfo')

				// 更新页面状态
				isLoggedIn.value = false
				userInfo.value = {
					nickname: '',
					avatar: '',
					phone: ''
				}

				uni.showToast({
					title: '已退出登录',
					icon: 'success',
					duration: 2000
				})
			}
		}
	})
}

// 页面导航
const navigateTo = (url: string) => {
	uni.navigateTo({
		url,
		fail: (error: any) => {
			console.error('页面跳转失败:', error)
			uni.showToast({
				title: '页面暂未开放',
				icon: 'none',
				duration: 2000
			})
		}
	})
}

// 生命周期
onMounted(() => {
	console.log('我的页面加载完成')
	checkDevelopmentMode()
	checkLoginStatus()
})

// 页面显示时重新检查登录状态
onShow(() => {
	console.log('我的页面显示')
	// 只有在不是加载状态时才重新检查，避免重复调用
	if (!isLoading.value) {
		checkLoginStatus()
	}
})
</script>

<style scoped>
.container {
	min-height: 100vh;
	background-color: #f5f5f5;
}

/* 登录部分样式 */
.login-section {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 100vh;
	padding: 40rpx;
}

.login-card {
	background: white;
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	width: 100%;
	max-width: 500rpx;
}

.login-icon {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 30rpx;
}

.login-title {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.login-subtitle {
	display: block;
	font-size: 28rpx;
	color: #666;
	margin-bottom: 60rpx;
}

.login-btn {
	background: linear-gradient(135deg, #007AFF, #5AC8FA);
	color: white;
	border: none;
	border-radius: 50rpx;
	padding: 24rpx 60rpx;
	font-size: 32rpx;
	width: 100%;
	margin: 0;
}

.login-btn-text {
	color: white;
	font-weight: bold;
}

/* 加载状态样式 */
.loading-section {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background-color: #f5f5f5;
}

.loading-card {
	background: white;
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	display: flex;
	flex-direction: column;
	align-items: center;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #007AFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 30rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666;
	text-align: center;
}

/* 用户信息部分样式 */
.profile-section {
	padding: 0;
}

.user-header {
	background: linear-gradient(135deg, #007AFF, #5AC8FA);
	padding: 60rpx 40rpx 40rpx;
	display: flex;
	align-items: center;
	color: white;
}

.avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	margin-right: 30rpx;
	border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-info {
	flex: 1;
}

.nickname {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.phone {
	display: block;
	font-size: 28rpx;
	opacity: 0.8;
}

/* 菜单部分样式 */
.menu-section {
	padding: 20rpx;
}

.menu-group {
	background: white;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
}

.menu-item {
	display: flex;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.3s;
}

.menu-item:last-child {
	border-bottom: none;
}

.menu-item:active {
	background-color: #f5f5f5;
}

.menu-icon {
	font-size: 36rpx;
	margin-right: 30rpx;
	width: 40rpx;
	text-align: center;
}

.menu-text {
	flex: 1;
	font-size: 32rpx;
	color: #333;
}

.logout-text {
	color: #ff4757;
}

.menu-arrow {
	font-size: 28rpx;
	color: #ccc;
}

.logout-item .menu-arrow {
	color: #ff4757;
}

/* 停车场管理员专用菜单样式 */
.operator-item {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	margin-bottom: 2rpx;
}

.operator-item .menu-text {
	color: white;
	font-weight: bold;
}

.operator-item .menu-arrow {
	color: white;
}

.operator-item:active {
	background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 开发环境测试按钮样式 */
.dev-test-item {
	background-color: #f8f9fa;
	border: 2rpx dashed #007AFF;
}

.dev-test-item .menu-text {
	color: #007AFF;
	font-weight: bold;
}

.dev-test-item .menu-arrow {
	color: #007AFF;
}
</style>
