<template>
	<view class="container">
		<!-- 页面标题 -->
		<view class="page-header">
			<text class="page-title">共享停车服务流程</text>
			<text class="page-subtitle">简单四步，轻松停车</text>
		</view>

		<!-- 服务流程步骤 -->
		<view class="steps-container">
			<view class="step-item" v-for="(step, index) in serviceSteps" :key="index">
				<view class="step-number">{{ index + 1 }}</view>
				<view class="step-content">
					<view class="step-icon">{{ step.icon }}</view>
					<text class="step-title">{{ step.title }}</text>
					<text class="step-desc">{{ step.description }}</text>
					<view class="step-details">
						<text class="detail-item" v-for="detail in step.details" :key="detail">• {{ detail }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 常见问题 -->
		<view class="section">
			<view class="section-header">
				<view class="section-icon">❓</view>
				<text class="section-title">常见问题</text>
			</view>
			<view class="faq-list">
				<view class="faq-item" v-for="(faq, index) in faqList" :key="index" @click="toggleFaq(index)">
					<view class="faq-question">
						<text class="faq-q-text">{{ faq.question }}</text>
						<text class="faq-arrow" :class="{ 'expanded': faq.expanded }">></text>
					</view>
					<view class="faq-answer" v-if="faq.expanded">
						<text class="faq-a-text">{{ faq.answer }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 服务承诺 -->
		<view class="section">
			<view class="section-header">
				<view class="section-icon">🛡️</view>
				<text class="section-title">服务承诺</text>
			</view>
			<view class="promise-list">
				<view class="promise-item" v-for="promise in servicePromises" :key="promise.title">
					<view class="promise-icon">{{ promise.icon }}</view>
					<view class="promise-content">
						<text class="promise-title">{{ promise.title }}</text>
						<text class="promise-desc">{{ promise.description }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 联系我们 -->
		<view class="contact-section">
			<text class="contact-title">需要帮助？</text>
			<text class="contact-desc">我们的客服团队随时为您服务</text>
			<button class="contact-btn" @click="navigateTo('/pages/service/service')">
				<text class="contact-btn-text">联系客服</text>
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue'

// 服务流程步骤
const serviceSteps = ref([
	{
		icon: '🔍',
		title: '查找停车场',
		description: '使用位置定位或搜索功能找到合适的停车场',
		details: [
			'自动定位附近停车场',
			'按距离、价格筛选',
			'查看停车场详细信息',
			'了解收费标准和服务设施'
		]
	},
	{
		icon: '📅',
		title: '预定车位',
		description: '选择停车时间和车辆信息，完成预定',
		details: [
			'选择停车开始和结束时间',
			'输入或选择车牌号码',
			'查看费用明细',
			'选择可用优惠券'
		]
	},
	{
		icon: '💳',
		title: '在线支付',
		description: '使用微信支付完成订单支付',
		details: [
			'支持微信支付',
			'费用透明，无隐藏收费',
			'支付成功后生成订单',
			'可使用优惠券抵扣费用'
		]
	},
	{
		icon: '🚗',
		title: '到场停车',
		description: '按时到达停车场，享受便捷停车服务',
		details: [
			'订单详情包含停车场地址',
			'可一键复制地址到地图导航',
			'联系停车场获取帮助',
			'停车结束后订单自动完成'
		]
	}
])

// 常见问题
const faqList = ref([
	{
		question: '如何取消已预定的订单？',
		answer: '在订单详情页面，未开始的订单可以点击"取消订单"按钮进行取消。取消后费用将原路退回。',
		expanded: false
	},
	{
		question: '优惠券如何使用？',
		answer: '在预定页面选择可用的优惠券，系统会自动计算优惠后的价格。每个订单只能使用一张优惠券。',
		expanded: false
	},
	{
		question: '停车费用如何计算？',
		answer: '停车费用根据停车场的收费标准和停车天数计费',
		expanded: false
	},
	{
		question: '如果超时停车怎么办？',
		answer: '超时停车会按照停车场的收费标准额外收费，建议合理安排停车时间或提前延长订单。',
		expanded: false
	},
	{
		question: '可以修改车牌号码吗？',
		answer: '订单生成后暂不支持修改车牌号码，请在预定时仔细核对车牌信息。',
		expanded: false
	}
])

// 服务承诺
const servicePromises = ref([
	{
		icon: '🔒',
		title: '信息安全',
		description: '严格保护用户隐私，确保个人信息安全'
	},
	{
		icon: '⚡',
		title: '快速响应',
		description: '7×24小时客服支持，问题快速解决'
	},
	{
		icon: '💰',
		title: '价格透明',
		description: '明码标价，无隐藏费用，让您停车更放心'
	},
	{
		icon: '🎯',
		title: '服务保障',
		description: '专业团队运营，提供优质的停车服务体验'
	}
])

// 切换FAQ展开状态
const toggleFaq = (index) => {
	faqList.value[index].expanded = !faqList.value[index].expanded
}

// 页面导航
const navigateTo = (url) => {
	uni.navigateTo({
		url,
		fail: (error) => {
			console.error('页面跳转失败:', error)
			uni.showToast({
				title: '页面暂未开放',
				icon: 'none',
				duration: 2000
			})
		}
	})
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background-color: #f5f5f5;
}

/* 页面头部 */
.page-header {
	background: linear-gradient(135deg, #007AFF, #5AC8FA);
	padding: 60rpx 40rpx 40rpx;
	text-align: center;
	color: white;
}

.page-title {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 15rpx;
}

.page-subtitle {
	display: block;
	font-size: 28rpx;
	opacity: 0.9;
}

/* 服务流程步骤 */
.steps-container {
	padding: 40rpx 20rpx;
}

.step-item {
	display: flex;
	margin-bottom: 40rpx;
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.step-number {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #007AFF, #5AC8FA);
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: bold;
	margin-right: 30rpx;
	flex-shrink: 0;
}

.step-content {
	flex: 1;
}

.step-icon {
	font-size: 40rpx;
	margin-bottom: 15rpx;
}

.step-title {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.step-desc {
	display: block;
	font-size: 28rpx;
	color: #666;
	margin-bottom: 20rpx;
	line-height: 1.5;
}

.step-details {
	background: #f8f9fa;
	border-radius: 10rpx;
	padding: 20rpx;
}

.detail-item {
	display: block;
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
	line-height: 1.4;
}

.detail-item:last-child {
	margin-bottom: 0;
}

/* 通用section样式 */
.section {
	background: white;
	margin: 20rpx;
	border-radius: 20rpx;
	overflow: hidden;
}

.section-header {
	display: flex;
	align-items: center;
	padding: 30rpx 40rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.section-icon {
	font-size: 36rpx;
	margin-right: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

/* FAQ样式 */
.faq-list {
	padding: 0;
}

.faq-item {
	border-bottom: 1rpx solid #f0f0f0;
}

.faq-item:last-child {
	border-bottom: none;
}

.faq-question {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 40rpx;
	transition: background-color 0.3s;
}

.faq-question:active {
	background-color: #f5f5f5;
}

.faq-q-text {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

.faq-arrow {
	font-size: 24rpx;
	color: #ccc;
	transition: transform 0.3s;
}

.faq-arrow.expanded {
	transform: rotate(90deg);
}

.faq-answer {
	padding: 0 40rpx 30rpx;
	background: #f8f9fa;
}

.faq-a-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

/* 服务承诺样式 */
.promise-list {
	padding: 20rpx 0;
}

.promise-item {
	display: flex;
	align-items: center;
	padding: 25rpx 40rpx;
}

.promise-icon {
	font-size: 36rpx;
	margin-right: 30rpx;
	width: 40rpx;
	text-align: center;
}

.promise-content {
	flex: 1;
}

.promise-title {
	display: block;
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.promise-desc {
	display: block;
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

/* 联系我们样式 */
.contact-section {
	background: white;
	margin: 20rpx;
	border-radius: 20rpx;
	padding: 40rpx;
	text-align: center;
}

.contact-title {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
}

.contact-desc {
	display: block;
	font-size: 26rpx;
	color: #666;
	margin-bottom: 30rpx;
}

.contact-btn {
	background: linear-gradient(135deg, #007AFF, #5AC8FA);
	color: white;
	border: none;
	border-radius: 50rpx;
	padding: 24rpx 60rpx;
	font-size: 28rpx;
	margin: 0;
}

.contact-btn-text {
	color: white;
	font-weight: bold;
}
</style>