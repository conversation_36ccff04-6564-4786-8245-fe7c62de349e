<template>
  <view class="change-request-page">
    <view class="page-header">
      <text class="page-title">变更申请</text>
      <text class="page-desc">提交停车场信息变更申请</text>
    </view>

    <!-- 申请类型选择 -->
    <view class="request-type-section">
      <text class="section-title">申请类型</text>
      <view class="type-options">
        <view 
          v-for="type in requestTypes" 
          :key="type.value"
          class="type-option"
          :class="{ active: selectedType === type.value }"
          @click="selectType(type.value)"
        >
          <text class="type-name">{{ type.name }}</text>
          <text class="type-desc">{{ type.desc }}</text>
        </view>
      </view>
    </view>

    <!-- 停用申请 -->
    <view v-if="selectedType === 'close_dates'" class="form-section">
      <text class="section-title">停用申请确认</text>
      <view class="close-warning">
        <view class="warning-icon">⚠️</view>
        <view class="warning-content">
          <text class="warning-title">重要提醒</text>
          <text class="warning-desc">停用后，停车场将无法接受新的预订订单，现有订单不受影响。</text>
          <text class="warning-desc">如需重新启用，可以提交启用申请。</text>
        </view>
      </view>
    </view>

    <!-- 启用申请 -->
    <view v-if="selectedType === 'activate'" class="form-section">
      <text class="section-title">启用申请确认</text>
      <view class="activate-info">
        <view class="info-icon">✅</view>
        <view class="info-content">
          <text class="info-title">申请启用停车场</text>
          <text class="info-desc">启用后，停车场将可以接受新的预订订单。</text>
          <text class="info-desc">请确保停车场已准备好正常运营。</text>
        </view>
      </view>
    </view>

    <!-- 车位数修改申请 -->
    <view v-if="selectedType === 'modify_spaces'" class="form-section">
      <text class="section-title">车位数修改</text>
      <view class="spaces-form">
        <view class="current-spaces">
          <text>当前车位数：{{ currentParkingLot?.total_spaces || 0 }}</text>
        </view>
        <view class="new-spaces">
          <text>申请修改为：</text>
          <input 
            v-model.number="newSpaces" 
            type="number" 
            class="spaces-input"
            placeholder="请输入新的车位数"
          />
        </view>
      </view>
    </view>

    <!-- 图片修改申请 -->
    <view v-if="selectedType === 'modify_images'" class="form-section">
      <text class="section-title">图片修改</text>
      <view class="image-upload-section">
        <text class="upload-tip">请上传新的停车场图片</text>
        <view class="image-list">
          <view 
            v-for="(image, index) in newImages" 
            :key="index" 
            class="image-item"
          >
            <image :src="image" class="preview-image" />
            <button class="remove-image-btn" @click="removeImage(index)">删除</button>
          </view>
          <view class="upload-btn" @click="chooseImage">
            <text>+</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 基本信息修改申请 -->
    <view v-if="selectedType === 'modify_info'" class="form-section">
      <text class="section-title">基本信息修改</text>
      <view class="info-form">
        <view class="form-item">
          <text class="label">停车场名称：</text>
          <input 
            v-model="newInfo.name" 
            class="info-input"
            :placeholder="currentParkingLot?.name || '请输入停车场名称'"
          />
        </view>
        <view class="form-item">
          <text class="label">联系电话：</text>
          <input 
            v-model="newInfo.contact_phone" 
            class="info-input"
            :placeholder="currentParkingLot?.contact_phone || '请输入联系电话'"
          />
        </view>
        <view class="form-item">
          <text class="label">描述信息：</text>
          <textarea 
            v-model="newInfo.description" 
            class="info-textarea"
            :placeholder="currentParkingLot?.description || '请输入描述信息'"
          />
        </view>
      </view>
    </view>

    <!-- 申请原因 -->
    <view class="form-section">
      <text class="section-title">申请原因</text>
      <textarea 
        v-model="reason" 
        class="reason-textarea"
        placeholder="请详细说明申请原因"
      />
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button 
        class="submit-btn" 
        :disabled="!canSubmit || submitting"
        @click="submitRequest"
      >
        {{ submitting ? '提交中...' : '提交申请' }}
      </button>
    </view>

    <!-- 申请记录 -->
    <view class="history-section">
      <text class="section-title">申请记录</text>
      <view v-if="requestHistory.length === 0" class="empty-history">
        <text>暂无申请记录</text>
      </view>
      <view v-else class="history-list">
        <view 
          v-for="record in requestHistory" 
          :key="record.id" 
          class="history-item"
        >
          <view class="history-header">
            <text class="history-type">{{ getRequestTypeText(record.request_type) }}</text>
            <text class="history-status" :class="record.status">
              {{ getStatusText(record.status) }}
            </text>
          </view>
          <text class="history-reason">{{ record.reason }}</text>
          <text class="history-time">{{ formatDate(record.created_at) }}</text>
          <text v-if="record.admin_comment" class="admin-comment">
            管理员意见：{{ record.admin_comment }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import {
  submitChangeRequest,
  getOperatorChangeRequests,
  submitCloseDatesRequest,
  submitModifySpacesRequest,
  submitModifyImagesRequest,
  submitModifyInfoRequest
} from '../../api/changeRequest.js'
import { get } from '../../utils/request.js'

// 响应式数据
const selectedType = ref('')
const reason = ref('')
const submitting = ref(false)
const currentParkingLot = ref(null)
const requestHistory = ref([])

// 申请类型选项（动态计算）
const requestTypes = computed(() => {
  const types = [
    {
      value: 'modify_spaces',
      name: '车位数修改',
      desc: '申请修改停车场车位总数'
    },
    {
      value: 'modify_images',
      name: '图片修改',
      desc: '申请修改停车场展示图片'
    },
    {
      value: 'modify_info',
      name: '基本信息修改',
      desc: '申请修改停车场基本信息'
    }
  ]

  // 根据停车场状态添加启用/停用申请
  if (currentParkingLot.value) {
    if (currentParkingLot.value.status === 'approved') {
      // 如果是启用状态，显示停用申请
      types.unshift({
        value: 'close_dates',
        name: '停用申请',
        desc: '申请停用停车场（停车场将无法接受新订单）'
      })
    } else if (currentParkingLot.value.status === 'inactive') {
      // 如果是停用状态，显示启用申请
      types.unshift({
        value: 'activate',
        name: '启用申请',
        desc: '申请启用停车场（停车场将可以接受新订单）'
      })
    }
  }

  return types
})

// 关闭日期相关
const closedDates = ref([])

// 车位数修改相关
const newSpaces = ref(0)

// 图片修改相关
const newImages = ref([])

// 基本信息修改相关
const newInfo = ref({
  name: '',
  contact_phone: '',
  description: ''
})

// 计算属性
const canSubmit = computed(() => {
  if (!selectedType.value || !reason.value.trim()) {
    return false
  }
  
  switch (selectedType.value) {
    case 'close_dates':
      return true // 停用申请只需要填写原因即可
    case 'activate':
      return true // 启用申请只需要填写原因即可
    case 'modify_spaces':
      return newSpaces.value > 0
    case 'modify_images':
      return newImages.value.length > 0
    case 'modify_info':
      return newInfo.value.name || newInfo.value.contact_phone || newInfo.value.description
    default:
      return false
  }
})

// 方法
const selectType = (type) => {
  selectedType.value = type
  resetFormData()
}

const resetFormData = () => {
  closedDates.value = []
  newSpaces.value = 0
  newImages.value = []
  newInfo.value = {
    name: '',
    contact_phone: '',
    description: ''
  }
}

const addClosedDate = () => {
  closedDates.value.push({
    date: '',
    reason: ''
  })
}

const updateDate = (index, event) => {
  closedDates.value[index].date = event.detail.value
}

const removeDate = (index) => {
  closedDates.value.splice(index, 1)
}

const chooseImage = () => {
  uni.chooseImage({
    count: 6 - newImages.value.length,
    success: (res) => {
      // 这里需要上传图片到服务器
      // 简化处理，直接使用本地路径
      newImages.value.push(...res.tempFilePaths)
    }
  })
}

const removeImage = (index) => {
  newImages.value.splice(index, 1)
}

const submitRequest = async () => {
  if (!canSubmit.value) return
  
  try {
    submitting.value = true
    
    let response
    const parkingLotId = currentParkingLot.value?.id
    
    switch (selectedType.value) {
      case 'close_dates':
        // 停用申请
        response = await submitChangeRequest({
          parking_lot_id: parkingLotId,
          request_type: 'close_dates',
          request_data: { action: 'deactivate' },
          reason: reason.value
        })
        break
      case 'activate':
        // 启用申请
        response = await submitChangeRequest({
          parking_lot_id: parkingLotId,
          request_type: 'activate',
          request_data: { action: 'activate' },
          reason: reason.value
        })
        break
      case 'modify_spaces':
        response = await submitModifySpacesRequest(parkingLotId, newSpaces.value, reason.value)
        break
      case 'modify_images':
        response = await submitModifyImagesRequest(parkingLotId, newImages.value, reason.value)
        break
      case 'modify_info':
        response = await submitModifyInfoRequest(parkingLotId, newInfo.value, reason.value)
        break
    }
    
    if (response.success) {
      uni.showToast({
        title: '申请提交成功',
        icon: 'success'
      })
      
      // 重置表单
      selectedType.value = ''
      reason.value = ''
      resetFormData()
      
      // 刷新申请记录
      await loadRequestHistory()
    } else {
      uni.showToast({
        title: response.message || '提交失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('提交申请失败:', error)
    uni.showToast({
      title: '提交失败',
      icon: 'none'
    })
  } finally {
    submitting.value = false
  }
}

const loadParkingLotInfo = async () => {
  try {
    const response = await get('/api/parking-operator/parking-lot')
    
    if (response.success) {
      currentParkingLot.value = response.data
    }
  } catch (error) {
    console.error('加载停车场信息失败:', error)
    uni.showToast({
      title: error.message || '获取停车场信息失败',
      icon: 'none'
    })
  }
}

const loadRequestHistory = async () => {
  try {
    if (!currentParkingLot.value?.id) return
    
    const response = await getOperatorChangeRequests(currentParkingLot.value.id)
    if (response.success) {
      requestHistory.value = response.data.list || []
    }
  } catch (error) {
    console.error('加载申请记录失败:', error)
    // 不显示错误提示，因为可能是首次使用没有记录
  }
}

const getRequestTypeText = (type) => {
  const typeMap = {
    'close_dates': '停用申请',
    'activate': '启用申请',
    'modify_spaces': '车位数修改',
    'modify_images': '图片修改',
    'modify_info': '基本信息修改'
  }
  return typeMap[type] || '未知'
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'approved': '已批准',
    'rejected': '已拒绝'
  }
  return statusMap[status] || '未知'
}

const formatDate = (dateString) => {
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return '格式错误'
  }
}

// 生命周期
onMounted(async () => {
  await loadParkingLotInfo()
  await loadRequestHistory()
})
</script>

<style scoped>
.change-request-page {
  padding: 20rpx;
  background: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.page-desc {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.request-type-section,
.form-section,
.submit-section,
.history-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.type-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.type-option {
  padding: 30rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  transition: all 0.3s;
}

.type-option.active {
  border-color: #409eff;
  background: #f0f8ff;
}

.type-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.type-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.add-date-btn {
  background: #409eff;
  color: white;
  border: none;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.date-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.date-input {
  flex: 1;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  background: white;
}

.reason-input {
  flex: 2;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
}

.remove-btn {
  background: #f56c6c;
  color: white;
  border: none;
  padding: 15rpx 30rpx;
  border-radius: 8rpx;
}

.spaces-form {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.current-spaces,
.new-spaces {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.spaces-input {
  flex: 1;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  position: relative;
}

.preview-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
}

.remove-image-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background: #f56c6c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  font-size: 24rpx;
}

.upload-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #999;
}

.info-form {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.label {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.info-input,
.info-textarea {
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
}

.info-textarea {
  min-height: 120rpx;
}

.reason-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  resize: vertical;
}

.submit-btn {
  width: 100%;
  background: #409eff;
  color: white;
  border: none;
  padding: 30rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.submit-btn:disabled {
  background: #ccc;
}

.empty-history {
  text-align: center;
  color: #999;
  padding: 60rpx;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.history-item {
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.history-type {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.history-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.history-status.pending {
  background: #fff3cd;
  color: #856404;
}

.history-status.approved {
  background: #d4edda;
  color: #155724;
}

.history-status.rejected {
  background: #f8d7da;
  color: #721c24;
}

.history-reason {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.history-time {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 10rpx;
}

.admin-comment {
  font-size: 26rpx;
  color: #409eff;
  display: block;
  background: #f0f8ff;
  padding: 15rpx;
  border-radius: 8rpx;
  margin-top: 10rpx;
}

/* 停用申请警告样式 */
.close-warning {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  padding: 30rpx;
  background: #fff3cd;
  border: 2rpx solid #ffeaa7;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.warning-icon {
  font-size: 40rpx;
  flex-shrink: 0;
}

.warning-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.warning-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #856404;
  display: block;
}

.warning-desc {
  font-size: 26rpx;
  color: #856404;
  display: block;
  line-height: 1.4;
}

/* 启用申请信息样式 */
.activate-info {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  padding: 30rpx;
  background: #d4edda;
  border: 2rpx solid #c3e6cb;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.info-icon {
  font-size: 40rpx;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.info-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #155724;
  display: block;
}

.info-desc {
  font-size: 26rpx;
  color: #155724;
  display: block;
  line-height: 1.4;
}
</style>