<template>
  <view class="container">
    <!-- 页面标题 -->
    <view class="header">
      <text class="header-title">立即预定</text>
    </view>

    <!-- 停车场信息 -->
    <view class="parking-info" v-if="parkingLotInfo">
      <view class="info-row">
        <text class="info-label">停车场：</text>
        <text class="info-value">{{ parkingLotInfo.name }}</text>
      </view>
      <view class="info-row">
        <text class="info-label">地址：</text>
        <text class="info-value">{{ parkingLotInfo.address }}</text>
      </view>
    </view>

    <!-- 时间选择 -->
    <view class="section">
      <view class="section-title">选择停车时间</view>
      <view class="time-selector">
        <view class="time-item">
          <text class="time-label">入场时间</text>
          <view class="datetime-picker">
            <picker mode="date" :value="entryDate" @change="onEntryDateChange">
              <view class="datetime-value">
                {{ entryDate || "选择日期" }}
              </view>
            </picker>
            <picker mode="time" :value="entryTime" @change="onEntryTimeChange">
              <view class="datetime-value">
                {{ entryTime || "选择时间" }}
              </view>
            </picker>
          </view>
        </view>
        <view class="time-item">
          <text class="time-label">离场时间</text>
          <view class="datetime-picker">
            <picker mode="date" :value="exitDate" @change="onExitDateChange">
              <view class="datetime-value">
                {{ exitDate || "选择日期" }}
              </view>
            </picker>
            <picker mode="time" :value="exitTime" @change="onExitTimeChange">
              <view class="datetime-value">
                {{ exitTime || "选择时间" }}
              </view>
            </picker>
          </view>
        </view>
      </view>
    </view>

    <!-- 车辆选择 -->
    <view class="section">
      <view class="section-title">选择车辆</view>
      <VehicleSelector
        v-model="selectedVehicleId"
        title=""
        placeholder="请选择车辆或手动输入车牌号"
        :required="true"
        :auto-select-default="true"
        @change="onVehicleChange"
      />

      <!-- 手动输入车牌号选项 -->
      <view class="manual-input-toggle" @click="toggleManualInput">
        <text class="toggle-text">{{ showManualInput ? "使用车辆选择" : "手动输入车牌号" }}</text>
      </view>

      <!-- 手动输入车牌号 -->
      <view class="license-input" v-if="showManualInput">
        <textarea
          class="license-field license-textarea"
          v-model="licensePlate"
          placeholder="请输入车牌号，如：京A12345"
          maxlength="8"
          :auto-height="false"
          :show-confirm-bar="false"
          @input="onLicensePlateInput"
        />
      </view>
    </view>

    <!-- 优惠券选择 -->
    <view class="section">
      <view class="section-title">优惠券</view>
      <view class="coupon-selector">
        <textarea
          class="coupon-input coupon-textarea"
          v-model="couponCode"
          placeholder="请输入优惠券代码（可选）"
          :auto-height="false"
          :show-confirm-bar="false"
          @input="onCouponInput"
        />
        <button class="coupon-btn" @click="showCouponList">选择优惠券</button>
      </view>
    </view>

    <!-- 费用详情 -->
    <view class="section" v-if="priceInfo">
      <view class="section-title">费用详情</view>
      <view class="price-details">
        <view class="price-row">
          <text class="price-label">停车天数：</text>
          <text class="price-value">{{ priceInfo.duration_days || 0 }} 天</text>
        </view>
        <view class="price-row" v-if="priceInfo.price_breakdown && priceInfo.price_breakdown.description">
          <text class="price-label">计费说明：</text>
          <text class="price-value description">{{ priceInfo.price_breakdown.description }}</text>
        </view>
        <view class="price-row">
          <text class="price-label">停车费用：</text>
          <text class="price-value">¥{{ formatPrice(priceInfo.base_amount || 0) }}</text>
        </view>
        <view class="price-row" v-if="(priceInfo.discount_amount || 0) > 0">
          <text class="price-label">优惠金额：</text>
          <text class="price-value discount">-¥{{ formatPrice(priceInfo.discount_amount || 0) }}</text>
        </view>
        <view class="price-row total">
          <text class="price-label">应付金额：</text>
          <text class="price-value">¥{{ formatPrice(priceInfo.final_amount || 0) }}</text>
        </view>
        <!-- 价格规则提示 -->
        <view class="price-tips" v-if="priceInfo.duration_days">
          <view class="tips-title">💡 计费规则说明</view>
          <view class="tips-content">
            <text class="tips-text">• 采用累计天数收费模式，不足1天按1天计算</text>
            <text class="tips-text">• 1-7天使用累计价格，第8天起每天额外收费</text>
            <text class="tips-text" v-if="priceInfo.duration_days <= 7"> • 您停车{{ priceInfo.duration_days }}天，享受累计优惠价格 </text>
            <text class="tips-text" v-else> • 您停车{{ priceInfo.duration_days }}天，前7天累计价格+后续每天单独计费 </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="submit-btn" :class="{ disabled: !canSubmit }" :disabled="!canSubmit" @click="submitBooking">
        {{ submitButtonText }}
      </button>
    </view>

    <!-- 优惠券选择弹窗 -->
    <view class="coupon-modal" v-if="showCouponModal" @click="closeCouponModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">选择优惠券</text>
          <text class="modal-close" @click="closeCouponModal">×</text>
        </view>
        <view class="coupon-list">
          <view class="coupon-item" v-for="coupon in availableCoupons" :key="coupon.code" @click="selectCoupon(coupon)">
            <view class="coupon-info">
              <view class="coupon-name">{{ coupon.name }}</view>
              <view class="coupon-desc">{{ coupon.description }}</view>
              <view class="coupon-expiry">有效期至：{{ formatDate(coupon.expiry_date) }}</view>
            </view>
            <view class="coupon-value">
              <text v-if="coupon.type === 'fixed'" class="value-text">￥{{ coupon.value }}</text>
              <text v-else class="value-text">{{ Math.round((1 - coupon.value) * 10) }}折</text>
            </view>
          </view>
          <view v-if="availableCoupons.length === 0" class="empty-coupon">
            <text class="empty-text">暂无可用优惠券</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue"
import { onLoad } from "@dcloudio/uni-app"
import { getParkingLotById, getParkingPrice, createParkingOrder, getAvailableCoupons } from "../../api/parking.js"
import { initiatePayment, getUserOpenid } from "../../utils/payment.js"
import VehicleSelector from "../../components/VehicleSelector.vue"

// 响应式数据
const parkingLotId = ref<number>(0)
const parkingLotInfo = ref<any>(null)
const entryDate = ref<string>("")
const entryTime = ref<string>("")
const exitDate = ref<string>("")
const exitTime = ref<string>("")
const licensePlate = ref<string>("")
const selectedVehicleId = ref<string>("")
const showManualInput = ref<boolean>(false)
const couponCode = ref<string>("")
const priceInfo = ref<any>(null)
const availableCoupons = ref<any[]>([])
const showCouponModal = ref<boolean>(false)
const loading = ref<boolean>(false)

// 计算属性
const canSubmit = computed(() => {
  return (
    parkingLotId.value &&
    entryDate.value &&
    entryTime.value &&
    exitDate.value &&
    exitTime.value &&
    licensePlate.value.trim() &&
    priceInfo.value &&
    !loading.value
  )
})

const submitButtonText = computed(() => {
  if (loading.value) return "处理中..."
  if (!canSubmit.value) return "请完善信息"
  if (priceInfo.value && priceInfo.value.final_amount !== undefined) {
    return `立即预定 ¥${priceInfo.value.final_amount}`
  }
  return "立即预定"
})

// 初始化默认时间
const initDefaultTimes = () => {
  const now = new Date()
  const entry = new Date(now.getTime() + 30 * 60 * 1000) // 30分钟后
  const exit = new Date(entry.getTime() + 2 * 60 * 60 * 1000) // 2小时后

  // 设置入场日期和时间
  entryDate.value = entry.toISOString().slice(0, 10) // YYYY-MM-DD
  entryTime.value = entry.toTimeString().slice(0, 5) // HH:MM

  // 设置离场日期和时间
  exitDate.value = exit.toISOString().slice(0, 10) // YYYY-MM-DD
  exitTime.value = exit.toTimeString().slice(0, 5) // HH:MM
}

// 日期时间选择处理
const onEntryDateChange = (e: any) => {
  entryDate.value = e.detail.value
  updatePriceInfo()
}

const onEntryTimeChange = (e: any) => {
  entryTime.value = e.detail.value
  updatePriceInfo()
}

const onExitDateChange = (e: any) => {
  exitDate.value = e.detail.value
  updatePriceInfo()
}

const onExitTimeChange = (e: any) => {
  exitTime.value = e.detail.value
  updatePriceInfo()
}

// 车辆选择处理
const onVehicleChange = (vehicle: any) => {
  if (vehicle) {
    licensePlate.value = vehicle.plate_number
    console.log("选择车辆:", vehicle)
  }
}

// 切换手动输入模式
const toggleManualInput = () => {
  showManualInput.value = !showManualInput.value
  if (!showManualInput.value) {
    // 切换回车辆选择模式时，清空手动输入的车牌号
    licensePlate.value = ""
  }
}

// 车牌输入处理
const onLicensePlateInput = () => {
  licensePlate.value = licensePlate.value.toUpperCase()
}

// 优惠券输入处理
const onCouponInput = () => {
  updatePriceInfo()
}

// 组合日期和时间
const combineDateTime = (date: string, time: string) => {
  if (!date || !time) return ""
  return `${date}T${time}:00`
}

// 更新价格信息
const updatePriceInfo = async () => {
  if (!parkingLotId.value || !entryDate.value || !entryTime.value || !exitDate.value || !exitTime.value) {
    priceInfo.value = null
    return
  }

  try {
    const entryDateTime = combineDateTime(entryDate.value, entryTime.value)
    const exitDateTime = combineDateTime(exitDate.value, exitTime.value)

    const response = await getParkingPrice(parkingLotId.value, entryDateTime, exitDateTime, couponCode.value)

    if (response.success) {
      priceInfo.value = response.data
    }
  } catch (error) {
    console.error("获取价格信息失败:", error)
    uni.showToast({
      title: "获取价格失败",
      icon: "none",
    })
  }
}

// 显示优惠券列表
const showCouponList = async () => {
  try {
    const response = await getAvailableCoupons()
    if (response.success) {
      availableCoupons.value = response.data
      showCouponModal.value = true
    }
  } catch (error) {
    console.error("获取优惠券列表失败:", error)
    uni.showToast({
      title: "获取优惠券失败",
      icon: "none",
    })
  }
}

// 关闭优惠券弹窗
const closeCouponModal = () => {
  showCouponModal.value = false
}

// 选择优惠券
const selectCoupon = (coupon: any) => {
  couponCode.value = coupon.code
  showCouponModal.value = false
  updatePriceInfo()

  uni.showToast({
    title: `已选择：${coupon.name}`,
    icon: "success",
  })
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ""
  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`
}

// 格式化价格
const formatPrice = (price: number) => {
  if (typeof price !== "number" || isNaN(price)) return "0.00"
  return price.toFixed(2)
}

// 提交预定
const submitBooking = async () => {
  if (!canSubmit.value) return

  // 验证车牌号格式
  const licensePlateRegex =
    /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/
  if (!licensePlateRegex.test(licensePlate.value.replace(/\s/g, ""))) {
    uni.showToast({
      title: "请输入正确的车牌号",
      icon: "none",
    })
    return
  }

  // 验证时间
  const entryDateTime = combineDateTime(entryDate.value, entryTime.value)
  const exitDateTime = combineDateTime(exitDate.value, exitTime.value)
  const entryDateObj = new Date(entryDateTime)
  const exitDateObj = new Date(exitDateTime)
  const now = new Date()

  if (exitDateObj <= entryDateObj) {
    uni.showToast({
      title: "离场时间必须晚于入场时间",
      icon: "none",
    })
    return
  }

  if (entryDateObj < now) {
    uni.showToast({
      title: "入场时间不能早于当前时间",
      icon: "none",
    })
    return
  }

  loading.value = true

  try {
    const orderData = {
      parking_lot_id: parkingLotId.value,
      entry_time: entryDateTime,
      exit_time: exitDateTime,
      license_plate: licensePlate.value,
      coupon_code: couponCode.value || undefined,
    }

    const response = await createParkingOrder(orderData)

    if (response.success) {
      uni.showModal({
        title: "预定成功",
        content: `订单号：${response.data.order_number}\n应付金额：¥${response.data.final_amount || 0}\n是否立即支付？`,
        confirmText: "立即支付",
        cancelText: "稍后支付",
        success: async (res) => {
          if (res.confirm) {
            // 用户选择立即支付
            try {
              uni.showLoading({
                title: "支付准备中...",
                mask: true,
              })

              // 获取用户openid
              const openid = await getUserOpenid()

              uni.hideLoading()
              uni.showLoading({
                title: "支付中...",
                mask: true,
              })

              // 发起支付
              const payResult = await initiatePayment({
                orderId: response.data.order_id,
                openid: openid
              })

              uni.hideLoading()

              if (payResult.success) {
                if (payResult.mock) {
                  // 模拟支付成功
                  uni.showToast({
                    title: "模拟支付成功",
                    icon: "success",
                  })
                } else {
                  // 真实支付成功
                  uni.showToast({
                    title: "支付成功",
                    icon: "success",
                  })
                }

                // 跳转到订单页面
                setTimeout(() => {
                  uni.switchTab({
                    url: "/pages/order/order",
                  })
                }, 1500)
              } else if (payResult.cancelled) {
                // 用户取消支付
                uni.showToast({
                  title: "支付已取消",
                  icon: "none",
                })
              } else {
                // 支付失败
                throw new Error(payResult.message || "支付失败")
              }
            } catch (error) {
              uni.hideLoading()
              console.error("支付失败:", error)
              uni.showToast({
                title: "支付失败，请到订单页面重试",
                icon: "none",
              })
              // 跳转到订单页面
              setTimeout(() => {
                uni.switchTab({
                  url: "/pages/order/order",
                })
              }, 2000)
            }
          } else {
            // 用户选择稍后支付，直接跳转到订单页面
            uni.switchTab({
              url: "/pages/order/order",
            })
          }
        },
      })
    } else {
      throw new Error(response.message || "预定失败")
    }
  } catch (error: any) {
    console.error("创建订单失败:", error)
    uni.showToast({
      title: error.message || "预定失败",
      icon: "none",
    })
  } finally {
    loading.value = false
  }
}

// 获取停车场信息
const loadParkingLotInfo = async () => {
  if (!parkingLotId.value) return

  try {
    const response = await getParkingLotById(parkingLotId.value)
    if (response.success) {
      parkingLotInfo.value = response.data
    }
  } catch (error) {
    console.error("获取停车场信息失败:", error)
  }
}

// 页面加载时获取参数
onLoad((options: any) => {
  console.log("预定页面加载完成，参数:", options)

  // 从页面参数获取停车场ID
  if (options.parkingLotId) {
    parkingLotId.value = parseInt(options.parkingLotId)
    console.log("获取到停车场ID:", parkingLotId.value)
    loadParkingLotInfo()
  } else {
    uni.showModal({
      title: "提示",
      content: "请先选择停车场",
      success: () => {
        uni.navigateBack()
      },
    })
    return
  }

  // 初始化默认时间
  initDefaultTimes()

  // 如果有停车场ID，立即计算价格
  if (parkingLotId.value) {
    updatePriceInfo()
  }
})
</script>

<style scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.header {
  background-color: #fff;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #eee;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.parking-info {
  background-color: #fff;
  margin: 20rpx 0;
  padding: 30rpx 40rpx;
}

.info-row {
  display: flex;
  margin-bottom: 20rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #666;
  font-size: 28rpx;
  width: 140rpx;
  flex-shrink: 0;
}

.info-value {
  color: #333;
  font-size: 28rpx;
  flex: 1;
}

.section {
  background-color: #fff;
  margin: 20rpx 0;
  padding: 30rpx 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.time-selector {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.time-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.time-label {
  color: #666;
  font-size: 28rpx;
  width: 140rpx;
}

.datetime-picker {
  display: flex;
  gap: 20rpx;
  flex: 1;
  margin-left: 20rpx;
}

.datetime-value {
  color: #333;
  font-size: 28rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  flex: 1;
  text-align: center;
  min-width: 200rpx;
}

.time-value {
  color: #333;
  font-size: 28rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  flex: 1;
  margin-left: 20rpx;
  text-align: center;
}

.license-input {
  display: flex;
}

.license-field {
  flex: 1;
  padding: 25rpx 30rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 32rpx;
  color: #333;
  background-color: #fff;
}

.license-field:focus {
  border-color: #007aff;
}

.coupon-selector {
  display: flex;
  gap: 20rpx;
}

.coupon-input {
  flex: 1;
  padding: 25rpx 30rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;
}

.coupon-input:focus {
  border-color: #007aff;
}

.coupon-btn {
  padding: 25rpx 30rpx;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  white-space: nowrap;
}

.price-details {
  background-color: #f8f8f8;
  padding: 30rpx;
  border-radius: 8rpx;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.price-row:last-child {
  margin-bottom: 0;
}

.price-row.total {
  border-top: 1rpx solid #ddd;
  padding-top: 20rpx;
  margin-top: 20rpx;
  font-weight: bold;
}

.price-label {
  color: #666;
  font-size: 28rpx;
}

.price-value {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

.price-value.discount {
  color: #ff4757;
}

.total .price-label,
.total .price-value {
  color: #333;
  font-size: 32rpx;
  font-weight: bold;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 30rpx 40rpx;
  border-top: 1rpx solid #eee;
}

.submit-btn {
  width: 100%;
  padding: 30rpx;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.submit-btn.disabled {
  background-color: #ccc;
  color: #999;
}

.coupon-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  width: 80%;
  max-height: 70%;
  border-radius: 12rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}

.coupon-list {
  max-height: 60vh;
  overflow-y: auto;
}

.coupon-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.coupon-item:last-child {
  border-bottom: none;
}

.coupon-item:active {
  background-color: #f8f8f8;
}

.coupon-info {
  flex: 1;
}

.coupon-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.coupon-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.coupon-expiry {
  font-size: 24rpx;
  color: #999;
}

.coupon-value {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  border-radius: 8rpx;
  margin-left: 20rpx;
}

.value-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #fff;
}

.empty-coupon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.manual-input-toggle {
  margin-top: 20rpx;
  padding: 16rpx 0;
  text-align: center;
  border-top: 1rpx solid #eee;
}

.toggle-text {
  font-size: 26rpx;
  color: #007aff;
  text-decoration: underline;
}

.license-input {
  margin-top: 20rpx;
}

.license-textarea {
  height: 80rpx;
  line-height: 32rpx;
  resize: none;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
}

.coupon-textarea {
  height: 80rpx;
  line-height: 32rpx;
  resize: none;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
}

/* 价格提示样式 */
.price-tips {
  margin-top: 30rpx;
  padding: 24rpx;
  background-color: #f0f8ff;
  border-radius: 8rpx;
  border-left: 4rpx solid #007aff;
}

.tips-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 16rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tips-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.price-value.description {
  color: #007aff;
  font-weight: 500;
  word-break: break-all;
  white-space: pre-wrap;
}
</style>
