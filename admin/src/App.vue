<script setup lang="ts">
import { RouterView, useRoute } from 'vue-router'
import { computed } from 'vue'
import AppNavbar from '@/components/AppNavbar.vue'
import { useUserStore } from '@/stores/user'

const route = useRoute()
const userStore = useUserStore()

// 计算是否显示导航栏（登录页不显示）
const showNavbar = computed(() => {
  return route.path !== '/login' && userStore.isLoggedIn
})
</script>

<template>
  <div id="app">
    <!-- 导航栏 -->
    <AppNavbar v-if="showNavbar" />

    <!-- 主要内容区域 -->
    <div class="app-content" :class="{ 'with-navbar': showNavbar }">
      <RouterView />
    </div>
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f5f5f5;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-content {
  flex: 1;
  min-height: 100vh;
}

.app-content.with-navbar {
  min-height: calc(100vh - 64px);
}
</style>
