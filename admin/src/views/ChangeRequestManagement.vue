<template>
  <div class="change-request-management">
    <div class="page-header">
      <h1>变更申请管理</h1>
      <p>管理停车场运营商提交的各类变更申请</p>
    </div>

    <!-- 操作区域 -->
    <div class="operation-bar">
      <div class="search-area">
        <el-input v-model="searchForm.parking_lot_name" placeholder="请输入停车场名称搜索" style="width: 300px" clearable
          @keyup.enter="handleSearch" @clear="handleSearch">
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>

        <el-select v-model="searchForm.status" placeholder="选择状态" style="width: 150px; margin-left: 10px" clearable
          @change="handleSearch">
          <el-option label="待处理" value="pending" />
          <el-option label="已批准" value="approved" />
          <el-option label="已拒绝" value="rejected" />
        </el-select>

        <el-select v-model="searchForm.request_type" placeholder="申请类型" style="width: 150px; margin-left: 10px"
          clearable @change="handleSearch">
          <el-option label="停用申请" value="close_dates" />
          <el-option label="启用申请" value="activate" />
          <el-option label="修改车位数" value="modify_spaces" />
          <el-option label="修改图片" value="modify_images" />
          <el-option label="修改信息" value="modify_info" />
        </el-select>

        <el-button type="primary" @click="handleSearch" :loading="loading">
          <el-icon>
            <Search />
          </el-icon>
          搜索
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="tableData" :loading="loading" stripe style="width: 100%">
        <el-table-column prop="id" label="申请ID" width="80" />

        <el-table-column prop="parking_lot_name" label="停车场名称" min-width="150" show-overflow-tooltip />

        <el-table-column prop="request_type" label="申请类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getRequestTypeTagType(row.request_type)" size="small">
              {{ getRequestTypeText(row.request_type) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="applicant_name" label="申请人" width="120">
          <template #default="{ row }">
            <div>{{ row.applicant_name || '未知' }}</div>
            <div class="text-gray text-xs">{{ row.applicant_phone || '' }}</div>
          </template>
        </el-table-column>

        <el-table-column prop="reason" label="申请原因" min-width="200" show-overflow-tooltip />

        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="申请时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="handleView(row)">
                <el-icon>
                  <View />
                </el-icon>
                查看
              </el-button>
              <el-button v-if="row.status === 'pending'" type="success" size="small" @click="handleApprove(row)">
                <el-icon>
                  <Check />
                </el-icon>
                批准
              </el-button>
              <el-button v-if="row.status === 'pending'" type="danger" size="small" @click="handleReject(row)">
                <el-icon>
                  <Close />
                </el-icon>
                拒绝
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination v-model:current-page="pagination.page" v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </el-card>

    <!-- 查看申请详情对话框 -->
    <el-dialog v-model="detailDialogVisible" :title="`申请详情 - ${currentRequest?.parking_lot_name || ''}`" width="800px"
      :close-on-click-modal="false">
      <div v-if="currentRequest" class="request-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="申请ID">{{ currentRequest.id }}</el-descriptions-item>
            <el-descriptions-item label="申请类型">
              <el-tag :type="getRequestTypeTagType(currentRequest.request_type)" size="small">
                {{ getRequestTypeText(currentRequest.request_type) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="申请人">{{ currentRequest.applicant_name || '未知' }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ currentRequest.applicant_phone || '无' }}</el-descriptions-item>
            <el-descriptions-item label="申请时间">{{ formatDate(currentRequest.created_at) }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusTagType(currentRequest.status)" size="small">
                {{ getStatusText(currentRequest.status) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 申请原因 -->
        <div class="detail-section">
          <h3>申请原因</h3>
          <div class="reason-content">
            {{ currentRequest.reason || '无' }}
          </div>
        </div>

        <!-- 变更内容 -->
        <div class="detail-section">
          <h3>变更内容</h3>
          <div class="change-content">
            <!-- 关闭日期申请 -->
            <div v-if="currentRequest.request_type === 'close_dates'">
              <h4>申请关闭日期：</h4>
              <el-table :data="currentRequest.request_data.closed_dates || []" border>
                <el-table-column prop="date" label="关闭日期" width="150" />
                <el-table-column prop="reason" label="关闭原因" />
              </el-table>
            </div>

            <!-- 车位数修改申请 -->
            <div v-else-if="currentRequest.request_type === 'modify_spaces'">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="当前车位数">
                  {{ currentRequest.current_data?.total_spaces || 0 }}
                </el-descriptions-item>
                <el-descriptions-item label="申请修改为">
                  {{ currentRequest.request_data?.total_spaces || 0 }}
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 图片修改申请 -->
            <div v-else-if="currentRequest.request_type === 'modify_images'">
              <div class="image-comparison">
                <div class="current-images">
                  <h4>当前图片：</h4>
                  <div class="image-grid">
                    <el-image v-for="(url, index) in (currentRequest.current_data?.image_urls || [])"
                      :key="`current_${index}`" :src="getImageUrl(url)"
                      :preview-src-list="(currentRequest.current_data?.image_urls || []).map(getImageUrl)" fit="cover"
                      style="width: 100px; height: 100px; margin: 5px;" />
                  </div>
                </div>
                <div class="new-images">
                  <h4>申请修改为：</h4>
                  <div class="image-grid">
                    <el-image v-for="(url, index) in (currentRequest.request_data?.image_urls || [])"
                      :key="`new_${index}`" :src="getImageUrl(url)"
                      :preview-src-list="(currentRequest.request_data?.image_urls || []).map(getImageUrl)" fit="cover"
                      style="width: 100px; height: 100px; margin: 5px;" />
                  </div>
                </div>
              </div>
            </div>

            <!-- 基本信息修改申请 -->
            <div v-else-if="currentRequest.request_type === 'modify_info'">
              <el-descriptions :column="1" border>
                <el-descriptions-item v-if="currentRequest.request_data?.name" label="停车场名称">
                  <div class="change-item">
                    <span class="old-value">{{ currentRequest.current_data?.name || '无' }}</span>
                    <el-icon class="arrow-icon">
                      <ArrowRight />
                    </el-icon>
                    <span class="new-value">{{ currentRequest.request_data.name }}</span>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item v-if="currentRequest.request_data?.contact_phone" label="联系电话">
                  <div class="change-item">
                    <span class="old-value">{{ currentRequest.current_data?.contact_phone || '无' }}</span>
                    <el-icon class="arrow-icon">
                      <ArrowRight />
                    </el-icon>
                    <span class="new-value">{{ currentRequest.request_data.contact_phone }}</span>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item v-if="currentRequest.request_data?.description" label="描述信息">
                  <div class="change-item">
                    <div class="old-value">{{ currentRequest.current_data?.description || '无' }}</div>
                    <el-icon class="arrow-icon">
                      <ArrowRight />
                    </el-icon>
                    <div class="new-value">{{ currentRequest.request_data.description }}</div>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>

        <!-- 审批信息 -->
        <div v-if="currentRequest.status !== 'pending'" class="detail-section">
          <h3>审批信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="审批时间">
              {{ currentRequest.processed_at ? formatDate(currentRequest.processed_at) : '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="审批意见">
              {{ currentRequest.admin_comment || '无' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button v-if="currentRequest?.status === 'pending'" type="success" @click="handleApprove(currentRequest)">
            批准申请
          </el-button>
          <el-button v-if="currentRequest?.status === 'pending'" type="danger" @click="handleReject(currentRequest)">
            拒绝申请
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog v-model="processDialogVisible" :title="processAction === 'approve' ? '批准申请' : '拒绝申请'" width="500px"
      :close-on-click-modal="false">
      <div class="process-form">
        <el-form ref="processFormRef" :model="processForm" label-width="100px">
          <el-form-item label="审批意见">
            <el-input v-model="processForm.admin_comment" type="textarea" :rows="4"
              :placeholder="processAction === 'approve' ? '请输入批准意见（可选）' : '请输入拒绝原因'" />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="processDialogVisible = false">取消</el-button>
          <el-button :type="processAction === 'approve' ? 'success' : 'danger'" :loading="processLoading"
            @click="confirmProcess">
            {{ processAction === 'approve' ? '确认批准' : '确认拒绝' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, View, Check, Close, ArrowRight } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import {
  getAllChangeRequests,
  processChangeRequest,
  type ChangeRequest
} from '@/api/changeRequest'
import { API_CONFIG } from '@/config'

// 响应式数据
const loading = ref(false)
const processLoading = ref(false)
const tableData = ref<ChangeRequest[]>([])
const detailDialogVisible = ref(false)
const processDialogVisible = ref(false)
const currentRequest = ref<ChangeRequest | null>(null)
const processAction = ref<'approve' | 'reject'>('approve')

// 搜索表单
const searchForm = reactive({
  parking_lot_name: '',
  status: '',
  request_type: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 审批表单
const processFormRef = ref<FormInstance>()
const processForm = reactive({
  admin_comment: ''
})

// 方法定义
/**
 * 获取申请类型标签类型
 */
function getRequestTypeTagType(type: string): string {
  const typeMap: Record<string, string> = {
    'close_dates': 'warning',
    'modify_spaces': 'primary',
    'modify_images': 'success',
    'modify_info': 'info'
  }
  return typeMap[type] || 'info'
}

/**
 * 获取申请类型文本
 */
function getRequestTypeText(type: string): string {
  const textMap: Record<string, string> = {
    'close_dates': '停用申请',
    'activate': '启用申请',
    'modify_spaces': '修改车位数',
    'modify_images': '修改图片',
    'modify_info': '修改信息'
  }
  return textMap[type] || '未知'
}

/**
 * 获取状态标签类型
 */
function getStatusTagType(status: string): string {
  const typeMap: Record<string, string> = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

/**
 * 获取状态文本
 */
function getStatusText(status: string): string {
  const textMap: Record<string, string> = {
    'pending': '待处理',
    'approved': '已批准',
    'rejected': '已拒绝'
  }
  return textMap[status] || '未知'
}

/**
 * 格式化日期
 */
function formatDate(dateString: string): string {
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return '格式错误'
  }
}

/**
 * 获取图片完整URL
 */
function getImageUrl(url: string): string {
  if (!url) return ''
  if (url.startsWith('http')) return url
  return `${API_CONFIG.SERVER_URL}${url}`
}

/**
 * 加载申请列表
 */
async function loadChangeRequests() {
  try {
    loading.value = true

    const params = {
      page: pagination.page,
      limit: pagination.pageSize,
      parking_lot_name: searchForm.parking_lot_name.trim() || undefined,
      status: searchForm.status || undefined,
      request_type: searchForm.request_type || undefined
    }

    const response = await getAllChangeRequests(params)

    if (response.success) {
      tableData.value = response.data.list
      pagination.total = response.data.total
    }
  } catch (error) {
    console.error('加载申请列表失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

/**
 * 搜索处理
 */
function handleSearch() {
  pagination.page = 1
  loadChangeRequests()
}

/**
 * 分页大小改变
 */
function handleSizeChange(size: number) {
  pagination.pageSize = size
  pagination.page = 1
  loadChangeRequests()
}

/**
 * 当前页改变
 */
function handleCurrentChange(page: number) {
  pagination.page = page
  loadChangeRequests()
}

/**
 * 查看申请详情
 */
function handleView(row: ChangeRequest) {
  currentRequest.value = row
  detailDialogVisible.value = true
}

/**
 * 批准申请
 */
function handleApprove(row: ChangeRequest) {
  currentRequest.value = row
  processAction.value = 'approve'
  processForm.admin_comment = ''
  processDialogVisible.value = true
}

/**
 * 拒绝申请
 */
function handleReject(row: ChangeRequest) {
  currentRequest.value = row
  processAction.value = 'reject'
  processForm.admin_comment = ''
  processDialogVisible.value = true
}

/**
 * 确认审批
 */
async function confirmProcess() {
  if (!currentRequest.value) return

  try {
    processLoading.value = true

    const response = await processChangeRequest(currentRequest.value.id, {
      action: processAction.value,
      admin_comment: processForm.admin_comment
    })

    if (response.success) {
      ElMessage.success(response.message)
      processDialogVisible.value = false
      detailDialogVisible.value = false
      loadChangeRequests()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('审批失败:', error)
    ElMessage.error('操作失败')
  } finally {
    processLoading.value = false
  }
}

// 初始化
onMounted(() => {
  loadChangeRequests()
})
</script>

<style scoped>
.change-request-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.search-area {
  display: flex;
  align-items: center;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.text-gray {
  color: #999;
}

.text-xs {
  font-size: 12px;
}

.request-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.reason-content {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  min-height: 60px;
  white-space: pre-wrap;
}

.change-content {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.image-comparison {
  display: flex;
  gap: 20px;
}

.current-images,
.new-images {
  flex: 1;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.change-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.old-value {
  color: #999;
  text-decoration: line-through;
}

.new-value {
  color: #409eff;
  font-weight: 500;
}

.arrow-icon {
  color: #409eff;
}

.process-form {
  padding: 20px 0;
}
</style>