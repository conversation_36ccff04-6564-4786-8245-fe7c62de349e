<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h1>系统概览</h1>
      <p>欢迎使用共享停车管理系统，{{ userStore.userInfo?.username }}</p>
    </div>

    <div class="dashboard-content">
      <!-- 数据概览卡片 -->
      <el-row :gutter="20" v-loading="statsLoading">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="40" color="#67C23A">
                  <Money />
                </el-icon>
              </div>
              <div class="card-info">
                <h3>今日收入</h3>
                <p class="stats-number">¥{{ formatMoney(dashboardStats.todayRevenue) }}</p>
                <p class="stats-desc">今日已完成订单收入</p>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="40" color="#409EFF">
                  <Wallet />
                </el-icon>
              </div>
              <div class="card-info">
                <h3>总收入</h3>
                <p class="stats-number">¥{{ formatMoney(dashboardStats.totalRevenue) }}</p>
                <p class="stats-desc">平台累计收入</p>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="40" color="#E6A23C">
                  <UserFilled />
                </el-icon>
              </div>
              <div class="card-info">
                <h3>今日新用户</h3>
                <p class="stats-number">{{ dashboardStats.todayNewUsers }}</p>
                <p class="stats-desc">今日新注册用户数</p>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="40" color="#909399">
                  <User />
                </el-icon>
              </div>
              <div class="card-info">
                <h3>总用户数</h3>
                <p class="stats-number">{{ dashboardStats.totalUsers }}</p>
                <p class="stats-desc">平台注册用户总数</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px" v-loading="statsLoading">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="40" color="#F56C6C">
                  <Clock />
                </el-icon>
              </div>
              <div class="card-info">
                <h3>待处理订单</h3>
                <p class="stats-number">{{ dashboardStats.pendingOrders }}</p>
                <p class="stats-desc">进行中的订单数量</p>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="40" color="#67C23A">
                  <Location />
                </el-icon>
              </div>
              <div class="card-info">
                <h3>停车场总数</h3>
                <p class="stats-number">{{ dashboardStats.totalParkingLots }}</p>
                <p class="stats-desc">平台管理停车场数量</p>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="info-card clickable-card" @click="goToParkingLots">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="40" color="#409EFF">
                  <Setting />
                </el-icon>
              </div>
              <div class="card-info">
                <h3>停车场管理</h3>
                <p>点击进入停车场管理</p>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="info-card clickable-card" @click="goToOrderManagement">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="40" color="#E6A23C">
                  <Document />
                </el-icon>
              </div>
              <div class="card-info">
                <h3>订单管理</h3>
                <p>查看和管理订单</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px" v-if="userStore.isSuperAdmin">
        <el-col :span="6">
          <el-card class="info-card clickable-card" @click="goToAdminManagement">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="40" color="#909399">
                  <User />
                </el-icon>
              </div>
              <div class="card-info">
                <h3>管理员设置</h3>
                <p>管理系统管理员账户</p>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="info-card clickable-card" @click="goToCouponManagement">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="40" color="#F56C6C">
                  <Ticket />
                </el-icon>
              </div>
              <div class="card-info">
                <h3>优惠券管理</h3>
                <p>管理优惠券模板</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <div class="welcome-message">
        <el-alert
          title="欢迎使用共享停车管理系统"
          type="success"
          description="系统已成功启动，您可以开始管理停车场相关业务。"
          show-icon
          :closable="false"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  User,
  Clock,
  Setting,
  Document,
  Money,
  Wallet,
  UserFilled,
  Location,
  Ticket
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { getDashboardStats, type DashboardStats } from '@/api/admin'

// 路由和状态管理
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const statsLoading = ref(false)
const dashboardStats = ref<DashboardStats>({
  todayRevenue: 0,
  totalRevenue: 0,
  todayNewUsers: 0,
  totalUsers: 0,
  pendingOrders: 0,
  totalParkingLots: 0
})

/**
 * 加载Dashboard统计数据
 */
async function loadDashboardStats() {
  try {
    statsLoading.value = true
    const response = await getDashboardStats()

    if (response.success) {
      dashboardStats.value = response.data
    } else {
      ElMessage.error('获取统计数据失败')
    }
  } catch (error) {
    console.error('获取Dashboard统计数据错误:', error)
    ElMessage.error('获取统计数据失败，请稍后重试')
  } finally {
    statsLoading.value = false
  }
}

/**
 * 格式化金额显示
 */
function formatMoney(amount: number): string {
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

/**
 * 跳转到停车场管理页面
 */
function goToParkingLots() {
  router.push('/parking-lots')
}

/**
 * 跳转到订单管理页面
 */
function goToOrderManagement() {
  router.push('/orders')
}

/**
 * 跳转到管理员管理页面
 */
function goToAdminManagement() {
  router.push('/admins')
}

/**
 * 跳转到优惠券管理页面
 */
function goToCouponManagement() {
  router.push('/coupon-templates')
}

// 组件挂载时加载数据
onMounted(() => {
  loadDashboardStats()
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.dashboard-header {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-header h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.dashboard-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.dashboard-content {
  space-y: 20px;
}

.stats-card {
  height: 120px;
}

.info-card {
  height: 120px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  margin-right: 15px;
}

.card-info h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
}

.card-info p {
  margin: 5px 0;
  color: #666;
  font-size: 12px;
}

.stats-number {
  font-size: 24px !important;
  font-weight: bold;
  color: #333 !important;
  margin: 8px 0 !important;
}

.stats-desc {
  font-size: 12px;
  color: #999 !important;
}

.welcome-message {
  margin-top: 30px;
}

.clickable-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.clickable-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-header {
    text-align: center;
  }

  .stats-number {
    font-size: 20px !important;
  }
}

@media (max-width: 576px) {
  .dashboard-container {
    padding: 10px;
  }

  .stats-number {
    font-size: 18px !important;
  }

  .card-info h3 {
    font-size: 14px;
  }
}
</style>
