import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: {
        title: '登录',
        requiresAuth: false
      }
    },
    {
      path: '/',
      name: 'Dashboard',
      component: () => import('@/views/Dashboard.vue'),
      meta: {
        title: '仪表板',
        requiresAuth: true
      }
    },
    {
      path: '/parking-lots',
      name: 'ParkingLotManagement',
      component: () => import('@/views/ParkingLotManagement.vue'),
      meta: {
        title: '停车场管理',
        requiresAuth: true
      }
    },
    {
      path: '/change-requests',
      name: 'ChangeRequestManagement',
      component: () => import('@/views/ChangeRequestManagement.vue'),
      meta: {
        title: '变更申请管理',
        requiresAuth: true
      }
    },
    {
      path: '/admins',
      name: 'AdminManagement',
      component: () => import('@/views/AdminManagement.vue'),
      meta: {
        title: '管理员设置',
        requiresAuth: true
      }
    },
    {
      path: '/orders',
      name: 'OrderManagement',
      component: () => import('@/views/OrderManagement.vue'),
      meta: {
        title: '订单管理',
        requiresAuth: true
      }
    },
    {
      path: '/users',
      name: 'UserManagement',
      component: () => import('@/views/UserManagement.vue'),
      meta: {
        title: '用户管理',
        requiresAuth: true
      }
    },
    {
      path: '/categories',
      name: 'CategoryManagement',
      component: () => import('@/views/CategoryManagement.vue'),
      meta: {
        title: '分类管理',
        requiresAuth: true
      }
    },
    {
      path: '/content-management',
      name: 'ContentManagement',
      component: () => import('@/views/ContentManagement.vue'),
      meta: {
        title: '轮播图管理',
        requiresAuth: true
      }
    },
    {
      path: '/platform-settings',
      name: 'PlatformSettings',
      component: () => import('@/views/PlatformSettings.vue'),
      meta: {
        title: '平台信息配置',
        requiresAuth: true
      }
    },
    {
      path: '/coupon-templates',
      name: 'CouponTemplateManagement',
      component: () => import('@/views/CouponTemplateManagement.vue'),
      meta: {
        title: '优惠券模板管理',
        requiresAuth: true
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      redirect: '/'
    }
  ],
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 共享停车管理系统`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 需要认证的页面
    if (!userStore.isLoggedIn) {
      // 未登录，重定向到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath } // 保存原始路径，登录后可以跳转回来
      })
      return
    }
  } else {
    // 不需要认证的页面（如登录页）
    if (to.path === '/login' && userStore.isLoggedIn) {
      // 已登录用户访问登录页，重定向到首页
      next('/')
      return
    }
  }

  next()
})

export default router
