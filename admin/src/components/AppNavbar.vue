<template>
  <div class="app-navbar">
    <div class="navbar-content">
      <!-- 左侧：Logo和导航 -->
      <div class="navbar-left">
        <div class="logo" @click="goHome">
          <el-icon size="24" color="#409EFF">
            <House />
          </el-icon>
          <span class="logo-text">共享停车管理系统</span>
        </div>

        <div class="nav-menu">
          <el-menu :default-active="activeMenu" mode="horizontal" :ellipsis="false" @select="handleMenuSelect">
            <el-menu-item index="/">
              <el-icon>
                <House />
              </el-icon>
              <span>首页</span>
            </el-menu-item>

            <el-sub-menu index="business">
              <template #title>
                <el-icon>
                  <OfficeBuilding />
                </el-icon>
                <span>业务管理</span>
              </template>
              <el-menu-item index="/parking-lots">
                <el-icon>
                  <OfficeBuilding />
                </el-icon>
                <span>停车场管理</span>
              </el-menu-item>
              <el-menu-item index="/change-requests">
                <el-icon>
                  <EditPen />
                </el-icon>
                <span>变更申请管理</span>
              </el-menu-item>
              <el-menu-item index="/orders">
                <el-icon>
                  <Document />
                </el-icon>
                <span>订单管理</span>
              </el-menu-item>
            </el-sub-menu>

            <el-menu-item index="/users">
              <el-icon>
                <User />
              </el-icon>
              <span>用户管理</span>
            </el-menu-item>

            <el-sub-menu index="content">
              <template #title>
                <el-icon>
                  <Files />
                </el-icon>
                <span>内容管理</span>
              </template>
              <el-menu-item index="/categories">
                <el-icon>
                  <Grid />
                </el-icon>
                <span>分类管理</span>
              </el-menu-item>
              <el-menu-item index="/content-management">
                <el-icon>
                  <Picture />
                </el-icon>
                <span>轮播图管理</span>
              </el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="marketing">
              <template #title>
                <el-icon>
                  <Ticket />
                </el-icon>
                <span>营销管理</span>
              </template>
              <el-menu-item index="/coupon-templates">
                <el-icon>
                  <Discount />
                </el-icon>
                <span>优惠券模板</span>
              </el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="system" v-if="userStore.isSuperAdmin">
              <template #title>
                <el-icon>
                  <Setting />
                </el-icon>
                <span>系统管理</span>
              </template>
              <el-menu-item index="/admins">
                <el-icon>
                  <UserFilled />
                </el-icon>
                <span>管理员设置</span>
              </el-menu-item>
              <el-menu-item index="/platform-settings">
                <el-icon>
                  <Tools />
                </el-icon>
                <span>平台信息配置</span>
              </el-menu-item>
            </el-sub-menu>
          </el-menu>
        </div>
      </div>

      <!-- 右侧：用户信息和操作 -->
      <div class="navbar-right">
        <div class="user-info">
          <el-avatar :size="32" class="user-avatar">
            {{ userStore.userInfo?.username?.charAt(0)?.toUpperCase() || 'A' }}
          </el-avatar>

          <el-dropdown @command="handleUserCommand">
            <span class="user-name">
              {{ userStore.userInfo?.username || '管理员' }}
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon>
                    <User />
                  </el-icon>
                  个人信息
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon>
                    <SwitchButton />
                  </el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import {
  House,
  OfficeBuilding,
  Document,
  User,
  UserFilled,
  Files,
  Grid,
  Picture,
  Setting,
  Tools,
  ArrowDown,
  SwitchButton,
  Ticket,
  Discount,
  EditPen
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

// 路由和状态管理
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 计算当前激活的菜单项
const activeMenu = computed(() => {
  const path = route.path

  // 根据路径确定激活的菜单项
  if (path === '/') return '/'
  if (path === '/users') return '/users'

  // 业务管理子菜单
  if (['/parking-lots', '/change-requests', '/orders'].includes(path)) {
    return path
  }

  // 内容管理子菜单
  if (['/categories', '/content-management'].includes(path)) {
    return path
  }

  // 营销管理子菜单
  if (['/coupon-templates'].includes(path)) {
    return path
  }

  // 系统管理子菜单
  if (['/admins', '/platform-settings'].includes(path)) {
    return path
  }

  return path
})

/**
 * 返回首页
 */
function goHome() {
  router.push('/')
}

/**
 * 菜单选择处理
 */
function handleMenuSelect(index: string) {
  if (index !== route.path) {
    router.push(index)
  }
}

/**
 * 用户下拉菜单命令处理
 */
async function handleUserCommand(command: string) {
  switch (command) {
    case 'profile':
      // 这里可以添加个人信息页面
      console.log('打开个人信息页面')
      break
    case 'logout':
      await handleLogout()
      break
  }
}

/**
 * 处理退出登录
 */
async function handleLogout() {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 执行登出
    userStore.logout()

    // 跳转到登录页
    await router.push('/login')
  } catch {
    // 用户取消操作
  }
}
</script>

<style scoped>
.app-navbar {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(8px);
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  padding: 0 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
  min-width: 0;
  /* 允许flex项目收缩 */
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px 12px;
  border-radius: 8px;
  min-width: 200px;
  white-space: nowrap;
  flex-shrink: 0;
  /* 防止logo被压缩 */
}

.logo:hover {
  background-color: #f5f7fa;
  transform: translateY(-1px);
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-menu {
  flex: 1;
  min-width: 0;
  /* 允许菜单收缩 */
  overflow: hidden;
  /* 防止菜单溢出 */
}

.nav-menu :deep(.el-menu) {
  border-bottom: none;
  background: transparent;
}

.nav-menu :deep(.el-menu-item) {
  border-bottom: 2px solid transparent;
  margin: 0 2px;
  border-radius: 6px 6px 0 0;
  transition: all 0.3s ease;
  padding: 0 12px;
}

.nav-menu :deep(.el-menu-item:hover) {
  background-color: #f5f7fa;
  border-bottom-color: #409eff;
}

.nav-menu :deep(.el-menu-item.is-active) {
  border-bottom-color: #409eff;
  color: #409eff;
  background-color: #f0f9ff;
}

.nav-menu :deep(.el-sub-menu) {
  margin: 0 2px;
}

.nav-menu :deep(.el-sub-menu .el-sub-menu__title) {
  padding: 0 12px;
  border-bottom: 2px solid transparent;
  border-radius: 6px 6px 0 0;
  transition: all 0.3s ease;
  position: relative;
}

/* 确保子菜单标题内容正确排列 */
.nav-menu :deep(.el-sub-menu .el-sub-menu__title) {
  display: flex !important;
  align-items: center;
  justify-content: flex-start;
}

/* 确保子菜单箭头显示并优化样式 */
.nav-menu :deep(.el-sub-menu .el-sub-menu__title .el-sub-menu__icon-arrow) {
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
  transition: transform 0.3s ease;
  font-size: 12px;
  color: #909399;
  position: relative;
  z-index: 10;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.nav-menu :deep(.el-sub-menu.is-opened .el-sub-menu__title .el-sub-menu__icon-arrow) {
  transform: rotateZ(180deg);
}

.nav-menu :deep(.el-sub-menu:hover .el-sub-menu__title .el-sub-menu__icon-arrow) {
  color: #409eff;
}

.nav-menu :deep(.el-sub-menu .el-sub-menu__title:hover) {
  background-color: #f5f7fa;
  border-bottom-color: #409eff;
}

.nav-menu :deep(.el-sub-menu.is-active .el-sub-menu__title) {
  border-bottom-color: #409eff;
  color: #409eff;
  background-color: #f0f9ff;
}

.nav-menu :deep(.el-menu--popup) {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
}

.nav-menu :deep(.el-menu--popup .el-menu-item) {
  margin: 2px 8px;
  border-radius: 6px;
  border-bottom: none;
  transition: all 0.3s ease;
}

.nav-menu :deep(.el-menu--popup .el-menu-item:hover) {
  background-color: #f5f7fa;
  border-bottom: none;
}

.navbar-right {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  /* 防止右侧用户信息被压缩 */
  margin-left: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

.user-name {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  color: #333;
  font-weight: 500;
}

.user-name:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

/* 桌面端优化 - 专注于管理后台的桌面使用场景 */
@media (max-width: 1400px) {
  .navbar-content {
    padding: 0 16px;
  }

  .navbar-left {
    gap: 16px;
  }

  .logo {
    min-width: 180px;
  }

  .logo-text {
    font-size: 18px;
  }
}

@media (max-width: 1200px) {
  .navbar-content {
    padding: 0 12px;
  }

  .navbar-left {
    gap: 12px;
  }

  .logo {
    min-width: 160px;
  }

  .logo-text {
    font-size: 16px;
  }

  .nav-menu :deep(.el-menu-item),
  .nav-menu :deep(.el-sub-menu .el-sub-menu__title) {
    padding: 0 10px;
  }
}

/* 最小宽度限制 - 管理后台不适合在过小的屏幕上使用 */
@media (max-width: 1024px) {
  .app-navbar::before {
    content: "管理后台建议在桌面端使用，以获得最佳体验";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #f56565;
    color: white;
    text-align: center;
    padding: 8px;
    font-size: 14px;
    z-index: 9999;
  }
  
  .navbar-content {
    margin-top: 40px;
  }
}
</style>
