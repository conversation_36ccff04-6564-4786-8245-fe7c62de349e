import request from './request'

// 价格配置相关接口类型
export interface PricingConfig {
  type: string
  daily_prices: {
    day_1: number
    day_2: number
    day_3: number
    day_4: number
    day_5: number
    day_6: number
    day_7: number
  }
  daily_price_after_7: number
  description?: string
}

export interface ParkingLotPricing {
  id: number
  name: string
  address: string
  status: string
  has_cumulative_pricing: boolean
  pricing_summary?: {
    day_1_price: number
    day_7_price: number
    daily_price_after_7: number
  }
  created_at: string
  updated_at: string
}

export interface ParkingLotPricingDetail {
  parking_lot_id: number
  parking_lot_name: string
  status: string
  price_rules: PricingConfig
}

export interface PricingListParams {
  page?: number
  limit?: number
  name?: string
  status?: string
}

export interface BatchDefaultPricingParams {
  parking_lot_ids: number[]
  default_config?: PricingConfig
}

export interface PricePreviewParams {
  parking_lot_id: number
  entry_time: string
  exit_time: string
}

export interface PricePreviewResult {
  parking_lot_id: number
  duration_days: number
  total_amount: number
  calculation_method: string
  price_breakdown: {
    days: number
    cumulative_price: number
    description: string
  }
}

// 定义接口类型
export interface LoginParams {
  username: string
  password: string
}

export interface LoginResponse {
  success: boolean
  message: string
  data: {
    token: string
    userInfo: {
      id: number
      username: string
      is_super_admin: number
      is_active: number
      created_at: string
    }
  }
}

export interface AdminInfo {
  id: number
  username: string
  is_super_admin: number
  is_active: number
  created_at: string
}

export interface ProfileResponse {
  success: boolean
  data: AdminInfo
}

export interface Admin {
  id: number
  username: string
  is_super_admin: number
  is_active: number
  created_at: string
  updated_at: string
}

export interface AdminListParams {
  page?: number
  pageSize?: number
  username?: string
}

export interface AdminListResponse {
  success: boolean
  data: {
    list: Admin[]
    total: number
    page: number
    pageSize: number
    totalPages: number
  }
}

export interface CreateAdminParams {
  username: string
  password: string
  is_super_admin?: boolean
  is_active?: boolean
}

export interface UpdateAdminParams {
  username?: string
  password?: string
  is_super_admin?: boolean
  is_active?: boolean
}

// 用户相关接口类型定义
export interface User {
  id: number
  nickname: string
  phone_number: string
  avatar_url?: string
  created_at: string
}

/**
 * 管理员登录
 * @param params 登录参数
 * @returns Promise<LoginResponse>
 */
export function login(params: LoginParams): Promise<LoginResponse> {
  return request({
    url: '/admin/login',
    method: 'POST',
    data: params
  })
}

/**
 * 获取当前登录管理员信息
 * @returns Promise<ProfileResponse>
 */
export function getProfile(): Promise<ProfileResponse> {
  return request({
    url: '/admin/profile',
    method: 'GET'
  })
}

/**
 * 管理员登出（前端处理）
 */
export function logout(): void {
  // 清除本地存储
  localStorage.removeItem('admin_token')
  localStorage.removeItem('admin_userInfo')

  // 跳转到登录页
  window.location.href = '/login'
}

/**
 * 获取管理员列表
 * @param params 查询参数
 * @returns Promise<AdminListResponse>
 */
export function getAdmins(params: AdminListParams): Promise<AdminListResponse> {
  return request({
    url: '/admin/admins',
    method: 'GET',
    params
  })
}

/**
 * 创建管理员
 * @param data 管理员数据
 * @returns Promise<any>
 */
export function createAdmin(data: CreateAdminParams): Promise<any> {
  return request({
    url: '/admin/admins',
    method: 'POST',
    data
  })
}

/**
 * 更新管理员
 * @param id 管理员ID
 * @param data 更新数据
 * @returns Promise<any>
 */
export function updateAdmin(id: number, data: UpdateAdminParams): Promise<any> {
  return request({
    url: `/admin/admins/${id}`,
    method: 'PUT',
    data
  })
}

/**
 * 删除管理员
 * @param id 管理员ID
 * @returns Promise<any>
 */
export function deleteAdmin(id: number): Promise<any> {
  return request({
    url: `/admin/admins/${id}`,
    method: 'DELETE'
  })
}

// Dashboard 相关接口类型定义
export interface DashboardStats {
  todayRevenue: number
  totalRevenue: number
  todayNewUsers: number
  totalUsers: number
  pendingOrders: number
  totalParkingLots: number
}

export interface DashboardStatsResponse {
  success: boolean
  data: DashboardStats
}

// 优惠券发放相关接口类型定义
export interface IssueCouponParams {
  template_id: number
  user_id: number
}

export interface IssueCouponResponse {
  success: boolean
  message: string
  data: {
    coupon_id: number
    user_nickname: string
    template_name: string
    expiry_date: string
  }
}

/**
 * 获取Dashboard统计数据
 * @returns Promise<DashboardStatsResponse>
 */
export function getDashboardStats(): Promise<DashboardStatsResponse> {
  return request({
    url: '/admin/dashboard/stats',
    method: 'GET'
  })
}

/**
 * 向用户发放优惠券
 * @param params 发放参数
 * @returns Promise<IssueCouponResponse>
 */
export function issueCouponToUser(params: IssueCouponParams): Promise<IssueCouponResponse> {
  return request({
    url: '/admin/coupons/issue',
    method: 'POST',
    data: params
  })
}

/**
 * 搜索用户（用于发放优惠券）
 * @param keyword 搜索关键词
 * @returns Promise<{ success: boolean, data: User[] }>
 */
export function searchUsers(keyword: string): Promise<{ success: boolean, data: User[] }> {
  return request({
    url: '/admin/users/search',
    method: 'GET',
    params: { keyword }
  })
}

// ==================== 价格配置管理相关API ====================

/**
 * 获取所有停车场的价格配置概览
 * @param params 查询参数
 * @returns Promise<{ success: boolean, data: { list: ParkingLotPricing[], total: number, page: number, limit: number, totalPages: number } }>
 */
export function getAllParkingLotsPricing(params: PricingListParams): Promise<{ success: boolean, data: { list: ParkingLotPricing[], total: number, page: number, limit: number, totalPages: number } }> {
  return request({
    url: '/admin/parking-lots/pricing',
    method: 'GET',
    params
  })
}

/**
 * 获取指定停车场的价格配置
 * @param id 停车场ID
 * @returns Promise<{ success: boolean, data: ParkingLotPricingDetail }>
 */
export function getParkingLotPricing(id: number): Promise<{ success: boolean, data: ParkingLotPricingDetail }> {
  return request({
    url: `/admin/parking-lots/${id}/pricing`,
    method: 'GET'
  })
}

/**
 * 更新指定停车场的价格配置
 * @param id 停车场ID
 * @param data 价格配置数据
 * @returns Promise<{ success: boolean, message: string, data: ParkingLotPricingDetail }>
 */
export function updateParkingLotPricing(id: number, data: { price_rules: PricingConfig }): Promise<{ success: boolean, message: string, data: ParkingLotPricingDetail }> {
  return request({
    url: `/admin/parking-lots/${id}/pricing`,
    method: 'PUT',
    data
  })
}

/**
 * 批量设置停车场的默认价格配置
 * @param data 批量设置参数
 * @returns Promise<{ success: boolean, message: string, data: { affected_count: number, default_config: PricingConfig } }>
 */
export function setDefaultPricing(data: BatchDefaultPricingParams): Promise<{ success: boolean, message: string, data: { affected_count: number, default_config: PricingConfig } }> {
  return request({
    url: '/admin/parking-lots/pricing/default',
    method: 'POST',
    data
  })
}

/**
 * 预览价格计算结果
 * @param params 预览参数
 * @returns Promise<{ success: boolean, data: PricePreviewResult }>
 */
export function previewPriceCalculation(params: PricePreviewParams): Promise<{ success: boolean, data: PricePreviewResult }> {
  return request({
    url: '/admin/pricing/preview',
    method: 'GET',
    params
  })
}
