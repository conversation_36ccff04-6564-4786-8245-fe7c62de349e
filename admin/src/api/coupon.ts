import request from './request'

// 定义接口类型
export interface CouponTemplate {
  id: number
  name: string
  type: 'fixed' | 'discount'
  value: number
  min_spend: number
  validity_type: 'fixed_days' | 'date_range'
  valid_days?: number
  valid_start_date?: string
  valid_end_date?: string
  total_quantity: number
  issued_quantity: number
  is_active: number
  created_at: string
  updated_at: string
}

export interface CouponTemplateListParams {
  page?: number
  pageSize?: number
  name?: string
}

export interface CouponTemplateListResponse {
  success: boolean
  data: {
    list: CouponTemplate[]
    total: number
    page: number
    pageSize: number
    totalPages: number
  }
}

export interface CreateCouponTemplateParams {
  name: string
  type: 'fixed' | 'discount'
  value: number
  min_spend?: number
  validity_type: 'fixed_days' | 'date_range'
  valid_days?: number
  valid_start_date?: string
  valid_end_date?: string
  total_quantity?: number
  is_active?: boolean
}

export interface UpdateCouponTemplateParams {
  name?: string
  type?: 'fixed' | 'discount'
  value?: number
  min_spend?: number
  validity_type?: 'fixed_days' | 'date_range'
  valid_days?: number
  valid_start_date?: string
  valid_end_date?: string
  total_quantity?: number
  is_active?: boolean
}

export interface ApiResponse {
  success: boolean
  message: string
  data?: any
}

/**
 * 获取优惠券模板列表
 * @param params 查询参数
 * @returns Promise<CouponTemplateListResponse>
 */
export function getCouponTemplates(params: CouponTemplateListParams): Promise<CouponTemplateListResponse> {
  return request({
    url: '/admin/coupon-templates',
    method: 'GET',
    params
  })
}

/**
 * 创建优惠券模板
 * @param data 优惠券模板数据
 * @returns Promise<ApiResponse>
 */
export function createCouponTemplate(data: CreateCouponTemplateParams): Promise<ApiResponse> {
  return request({
    url: '/admin/coupon-templates',
    method: 'POST',
    data
  })
}

/**
 * 更新优惠券模板
 * @param id 优惠券模板ID
 * @param data 更新数据
 * @returns Promise<ApiResponse>
 */
export function updateCouponTemplate(id: number, data: UpdateCouponTemplateParams): Promise<ApiResponse> {
  return request({
    url: `/admin/coupon-templates/${id}`,
    method: 'PUT',
    data
  })
}

/**
 * 删除优惠券模板
 * @param id 优惠券模板ID
 * @returns Promise<ApiResponse>
 */
export function deleteCouponTemplate(id: number): Promise<ApiResponse> {
  return request({
    url: `/admin/coupon-templates/${id}`,
    method: 'DELETE'
  })
}
