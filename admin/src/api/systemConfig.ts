import request from './request'

// 定义接口类型
export interface SystemConfig {
  config_key: string
  config_value: string
  description: string
}

export interface UpdateSystemConfigData {
  config_key: string
  config_value: string
  description?: string
}

/**
 * 获取系统配置
 * @param config_key 配置键
 * @returns 配置数据
 */
export const getSystemConfig = (config_key: string) => {
  return request.get<SystemConfig>('/admin/system-config', {
    params: { config_key }
  })
}

/**
 * 更新系统配置
 * @param data 配置数据
 * @returns 更新结果
 */
export const updateSystemConfig = (data: UpdateSystemConfigData) => {
  return request.post('/admin/system-config', data)
}
