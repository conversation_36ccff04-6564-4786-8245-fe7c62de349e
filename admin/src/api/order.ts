import request from './request'

// 定义接口类型
export interface Order {
  id: number
  order_number: string
  user_id: number
  user_nickname?: string
  user_phone?: string
  user_avatar?: string
  parking_lot_id: number
  parking_lot_name?: string
  parking_lot_address?: string
  parking_lot_phone?: string
  license_plate: string
  start_time: string
  end_time?: string
  total_amount: number
  discount_amount: number
  final_amount: number
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'refunded'
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
  payment_method?: string
  coupon_id?: number
  coupon_name?: string
  coupon_discount_amount?: number
  parking_info?: any
  payment_info?: any
  created_at: string
  updated_at: string
}

export interface OrderListParams {
  page?: number
  pageSize?: number
  order_number?: string
  license_plate?: string
  status?: string
  parking_lot_id?: number
  user_id?: number
  start_date?: string
  end_date?: string
}

export interface OrderListResponse {
  success: boolean
  data: {
    list: Order[]
    total: number
    page: number
    pageSize: number
    totalPages: number
  }
}

export interface OrderDetailResponse {
  success: boolean
  data: Order
}

/**
 * 获取订单列表
 * @param params 查询参数
 * @returns Promise<OrderListResponse>
 */
export function getOrders(params: OrderListParams = {}): Promise<OrderListResponse> {
  return request({
    url: '/admin/orders',
    method: 'GET',
    params
  })
}

/**
 * 获取单个订单详细信息
 * @param id 订单ID
 * @returns Promise<OrderDetailResponse>
 */
export function getOrderById(id: number): Promise<OrderDetailResponse> {
  return request({
    url: `/admin/orders/${id}`,
    method: 'GET'
  })
}

// 订单状态选项
export const ORDER_STATUS_OPTIONS = [
  { label: '待支付', value: 'pending_payment' },
  { label: '进行中', value: 'in_progress' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' }
]



/**
 * 获取订单状态文本
 */
export function getOrderStatusText(status: string): string {
  const option = ORDER_STATUS_OPTIONS.find(item => item.value === status)
  return option?.label || '未知状态'
}

/**
 * 获取订单状态标签类型
 */
export function getOrderStatusTagType(status: string): string {
  const typeMap: Record<string, string> = {
    'pending_payment': 'warning',
    'in_progress': 'primary',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return typeMap[status] || 'info'
}


