import request from '@/api/request'

// 申请记录接口类型定义
export interface ChangeRequest {
  id: number
  parking_lot_id: number
  parking_lot_name: string
  request_type: 'close_dates' | 'modify_spaces' | 'modify_images' | 'modify_info'
  request_data: any
  current_data: any
  reason: string
  status: 'pending' | 'approved' | 'rejected'
  applicant_name: string
  applicant_phone: string
  admin_comment?: string
  processed_at?: string
  created_at: string
}

export interface GetAllChangeRequestsParams {
  page?: number
  limit?: number
  status?: string
  request_type?: string
  parking_lot_name?: string
}

export interface ProcessChangeRequestParams {
  action: 'approve' | 'reject'
  admin_comment?: string
}

/**
 * 获取所有变更申请列表（管理员端）
 */
export function getAllChangeRequests(params: GetAllChangeRequestsParams) {
  return request.get<{
    list: ChangeRequest[]
    total: number
    page: number
    limit: number
    totalPages: number
  }>('/change-requests/admin/all', { params })
}

/**
 * 管理员审批申请
 */
export function processChangeRequest(requestId: number, data: ProcessChangeRequestParams) {
  return request.post(`/change-requests/admin/${requestId}/process`, data)
}