import request from './request'

// 定义接口类型
export interface User {
  id: number
  nickname: string
  phone_number: string
  avatar_url?: string
  created_at: string
}

export interface ParkingLotManager {
  id: number
  nickname: string
  phone_number: string
  email?: string
  avatar_url?: string
  created_at: string
  manager_relation_id: number
  // 编辑状态属性（前端使用）
  editingEmail?: boolean
  tempEmail?: string
  savingEmail?: boolean
}

export interface Admin {
  id: number
  username: string
  is_super_admin: number
  is_active: number
  created_at: string
  updated_at: string
}

export interface AdminListResponse {
  success: boolean
  data: {
    list: Admin[]
    total: number
    page: number
    pageSize: number
    totalPages: number
  }
}

export interface CreateAdminParams {
  username: string
  password: string
  is_super_admin?: boolean
  is_active?: boolean
}

export interface UpdateAdminParams {
  username?: string
  password?: string
  is_super_admin?: boolean
  is_active?: boolean
}

// ==================== 用户搜索 ====================

/**
 * 搜索用户（用于指派员工）
 * @param keyword 搜索关键词
 * @returns Promise<{ success: boolean, data: User[] }>
 */
export function searchUsers(keyword: string): Promise<{ success: boolean, data: User[] }> {
  return request({
    url: '/admin/users/search',
    method: 'GET',
    params: { keyword }
  })
}

// ==================== 停车场员工管理 ====================

/**
 * 获取指定停车场的所有管理员
 * @param parkingLotId 停车场ID
 * @returns Promise<{ success: boolean, data: ParkingLotManager[] }>
 */
export function getParkingLotManagers(parkingLotId: number): Promise<{ success: boolean, data: ParkingLotManager[] }> {
  return request({
    url: `/admin/parking-lots/${parkingLotId}/managers`,
    method: 'GET'
  })
}

/**
 * 为指定停车场添加管理员
 * @param parkingLotId 停车场ID
 * @param userId 用户ID
 * @returns Promise<any>
 */
export function addParkingLotManager(parkingLotId: number, userId: number): Promise<any> {
  return request({
    url: `/admin/parking-lots/${parkingLotId}/managers`,
    method: 'POST',
    data: { userId }
  })
}

/**
 * 移除指定停车场的管理员
 * @param parkingLotId 停车场ID
 * @param userId 用户ID
 * @returns Promise<any>
 */
export function removeParkingLotManager(parkingLotId: number, userId: number): Promise<any> {
  return request({
    url: `/admin/parking-lots/${parkingLotId}/managers/${userId}`,
    method: 'DELETE'
  })
}

/**
 * 更新停车场管理员邮箱
 * @param parkingLotId 停车场ID
 * @param userId 用户ID
 * @param email 邮箱地址
 * @returns Promise<any>
 */
export function updateParkingLotManagerEmail(parkingLotId: number, userId: number, email: string): Promise<any> {
  return request({
    url: `/admin/parking-lots/${parkingLotId}/managers/${userId}/email`,
    method: 'PUT',
    data: { email }
  })
}

// ==================== 后台管理员管理 ====================

/**
 * 获取管理员列表
 * @param params 查询参数
 * @returns Promise<AdminListResponse>
 */
export function getAdmins(params: {
  page?: number
  pageSize?: number
  username?: string
} = {}): Promise<AdminListResponse> {
  return request({
    url: '/admin/admins',
    method: 'GET',
    params
  })
}

/**
 * 创建新管理员
 * @param data 管理员数据
 * @returns Promise<any>
 */
export function createAdmin(data: CreateAdminParams): Promise<any> {
  return request({
    url: '/admin/admins',
    method: 'POST',
    data
  })
}

/**
 * 更新管理员信息
 * @param id 管理员ID
 * @param data 更新数据
 * @returns Promise<any>
 */
export function updateAdmin(id: number, data: UpdateAdminParams): Promise<any> {
  return request({
    url: `/admin/admins/${id}`,
    method: 'PUT',
    data
  })
}

/**
 * 删除管理员
 * @param id 管理员ID
 * @returns Promise<any>
 */
export function deleteAdmin(id: number): Promise<any> {
  return request({
    url: `/admin/admins/${id}`,
    method: 'DELETE'
  })
}
