import request from './request'

// 定义接口类型
export interface Category {
  id: number
  name: string
  icon_url: string
  sort_order: number
  created_at: string
}

export interface CreateCategoryData {
  name: string
  icon_url?: string
  sort_order?: number
}

export interface UpdateCategoryData {
  name?: string
  icon_url?: string
  sort_order?: number
}

/**
 * 获取分类列表
 * @returns 分类列表数据
 */
export const getCategoriesList = () => {
  return request.get<Category[]>('/admin/parking-lot-categories')
}

/**
 * 创建分类
 * @param data 分类数据
 * @returns 创建结果
 */
export const createCategory = (data: CreateCategoryData) => {
  return request.post('/admin/categories', data)
}

/**
 * 更新分类
 * @param id 分类ID
 * @param data 更新数据
 * @returns 更新结果
 */
export const updateCategory = (id: number, data: UpdateCategoryData) => {
  return request.put(`/admin/categories/${id}`, data)
}

/**
 * 删除分类
 * @param id 分类ID
 * @returns 删除结果
 */
export const deleteCategory = (id: number) => {
  return request.delete(`/admin/categories/${id}`)
}
