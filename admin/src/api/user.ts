import request from './request'

// 定义接口类型
export interface User {
  id: number
  openid: string
  nickname: string
  avatar_url: string
  phone_number: string
  created_at: string
}

export interface UserListParams {
  page?: number
  pageSize?: number
  nickname?: string
  phone_number?: string
}

export interface UserListResponse {
  list: User[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

/**
 * 获取用户列表
 * @param params 查询参数
 * @returns 用户列表数据
 */
export const getUsersList = (params: UserListParams) => {
  return request.get<UserListResponse>('/admin/users', { params })
}
