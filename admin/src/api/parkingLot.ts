import request from './request'

// 定义接口类型
export interface ParkingLot {
  id: number
  name: string
  category_id?: number
  category_name?: string
  address: string
  total_spaces: number
  monthly_sales: number
  rating: number
  airport_distance_km?: number
  station_distance_km?: number
  shuttle_time_minutes?: number
  contact_phone?: string
  backup_phone?: string
  business_documents?: any
  price_rules?: any
  service_facilities?: any[]
  description?: string
  image_urls?: string[]
  status: 'approved' | 'pending_review' | 'rejected' | 'inactive'
  created_at: string
  updated_at: string
}

export interface ParkingLotCategory {
  id: number
  name: string
  icon_url?: string
  sort_order: number
  created_at: string
}

export interface ParkingLotListParams {
  page?: number
  pageSize?: number
  name?: string
  status?: string
}

export interface ParkingLotListResponse {
  success: boolean
  data: {
    list: ParkingLot[]
    total: number
    page: number
    pageSize: number
    totalPages: number
  }
}

export interface ParkingLotDetailResponse {
  success: boolean
  data: ParkingLot
}

export interface ParkingLotCategoriesResponse {
  success: boolean
  data: ParkingLotCategory[]
}

export interface CreateParkingLotParams {
  name: string
  category_id?: number
  address: string
  total_spaces?: number
  monthly_sales?: number
  rating?: number
  airport_distance_km?: number
  station_distance_km?: number
  shuttle_time_minutes?: number
  contact_phone?: string
  backup_phone?: string
  business_documents?: any
  price_rules?: any
  service_facilities?: any[]
  description?: string
  image_urls?: string[]
}

export interface UpdateParkingLotParams extends Partial<CreateParkingLotParams> {
  status?: 'approved' | 'pending_review' | 'rejected' | 'inactive'
}

/**
 * 获取停车场列表
 * @param params 查询参数
 * @returns Promise<ParkingLotListResponse>
 */
export function getParkingLots(params: ParkingLotListParams = {}): Promise<ParkingLotListResponse> {
  return request({
    url: '/admin/parking-lots',
    method: 'GET',
    params
  })
}

/**
 * 获取单个停车场详细信息
 * @param id 停车场ID
 * @returns Promise<ParkingLotDetailResponse>
 */
export function getParkingLotById(id: number): Promise<ParkingLotDetailResponse> {
  return request({
    url: `/admin/parking-lots/${id}`,
    method: 'GET'
  })
}

/**
 * 创建新停车场
 * @param data 停车场数据
 * @returns Promise<any>
 */
export function createParkingLot(data: CreateParkingLotParams): Promise<any> {
  return request({
    url: '/admin/parking-lots',
    method: 'POST',
    data
  })
}

/**
 * 更新停车场信息
 * @param id 停车场ID
 * @param data 更新数据
 * @returns Promise<any>
 */
export function updateParkingLot(id: number, data: UpdateParkingLotParams): Promise<any> {
  return request({
    url: `/admin/parking-lots/${id}`,
    method: 'PUT',
    data
  })
}

/**
 * 删除停车场
 * @param id 停车场ID
 * @returns Promise<any>
 */
export function deleteParkingLot(id: number): Promise<any> {
  return request({
    url: `/admin/parking-lots/${id}`,
    method: 'DELETE'
  })
}

/**
 * 获取停车场分类列表
 * @returns Promise<ParkingLotCategoriesResponse>
 */
export function getParkingLotCategories(): Promise<ParkingLotCategoriesResponse> {
  return request({
    url: '/admin/parking-lot-categories',
    method: 'GET'
  })
}

// 评价管理相关接口类型
export interface Review {
  id: number
  order_id: number
  user_id: number
  parking_lot_id: number
  rating: number
  comment?: string
  status: 'visible' | 'hidden'
  created_at: string
  user_nickname?: string
  user_avatar?: string
  parking_lot_name?: string
  order_number?: string
  replies?: ReviewReply[]
}

export interface ReviewReply {
  id: number
  content: string
  created_at: string
  reply_user_nickname?: string
}

export interface ReviewListParams {
  page?: number
  pageSize?: number
  status?: string
}

export interface ReviewListResponse {
  success: boolean
  data: {
    list: Review[]
    total: number
    page: number
    pageSize: number
    totalPages: number
  }
}

/**
 * 获取停车场评价列表
 * @param parkingLotId 停车场ID
 * @param params 查询参数
 * @returns Promise<ReviewListResponse>
 */
export function getParkingLotReviews(parkingLotId: number, params: ReviewListParams = {}): Promise<ReviewListResponse> {
  return request({
    url: `/admin/parking-lots/${parkingLotId}/reviews`,
    method: 'GET',
    params
  })
}

/**
 * 更新评价状态
 * @param reviewId 评价ID
 * @param status 状态
 * @returns Promise<any>
 */
export function updateReviewStatus(reviewId: number, status: 'visible' | 'hidden'): Promise<any> {
  return request({
    url: `/admin/reviews/${reviewId}/status`,
    method: 'PUT',
    data: { status }
  })
}

/**
 * 回复评价
 * @param reviewId 评价ID
 * @param content 回复内容
 * @returns Promise<any>
 */
export function replyToReview(reviewId: number, content: string): Promise<any> {
  return request({
    url: `/admin/reviews/${reviewId}/reply`,
    method: 'POST',
    data: { content }
  })
}
