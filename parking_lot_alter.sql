-- Active: 1752723075833@@127.0.0.1@3306@parkStationSystem
-- -------------------------------------------------------------
-- 停车场信息表字段扩展 ALTER SQL
-- 添加月销量、评分、距离信息、备用电话、营业资料等字段
-- -------------------------------------------------------------

-- 为停车场信息表添加新字段
ALTER TABLE `parking_lots`
ADD COLUMN `monthly_sales` INT NOT NULL DEFAULT 0 COMMENT '月销量（订单数）' AFTER `total_spaces`,
ADD COLUMN `rating` DECIMAL(3, 2) NOT NULL DEFAULT 0.00 COMMENT '平均评分（1.00-5.00）' AFTER `monthly_sales`,
ADD COLUMN `airport_distance_km` DECIMAL(5, 2) NULL DEFAULT NULL COMMENT '距机场距离（公里）' AFTER `rating`,
ADD COLUMN `station_distance_km` DECIMAL(5, 2) NULL DEFAULT NULL COMMENT '距高铁站距离（公里）' AFTER `airport_distance_km`,
ADD COLUMN `shuttle_time_minutes` INT NULL DEFAULT NULL COMMENT '摆渡车时间（分钟）' AFTER `station_distance_km`,
ADD COLUMN `backup_phone` VARCHAR(50) NULL DEFAULT '' COMMENT '备用联系电话' AFTER `contact_phone`,
ADD COLUMN `business_documents` JSON NULL COMMENT '营业资料（营业执照、法人身份证、场地租赁合同等）JSON格式' AFTER `backup_phone`;

-- 添加索引以提高查询性能
ALTER TABLE `parking_lots`
ADD INDEX `idx_monthly_sales` (`monthly_sales`) COMMENT '月销量索引',
ADD INDEX `idx_rating` (`rating`) COMMENT '评分索引';

-- 创建评价管理相关的触发器，用于自动更新停车场评分
DELIMITER $$

-- 当有新评价时更新停车场平均评分
CREATE TRIGGER `update_parking_lot_rating_after_review_insert`
AFTER INSERT ON `reviews`
FOR EACH ROW
BEGIN
    UPDATE `parking_lots` 
    SET `rating` = (
        SELECT ROUND(AVG(rating), 2) 
        FROM `reviews` 
        WHERE `parking_lot_id` = NEW.parking_lot_id 
        AND `status` = 'visible'
    )
    WHERE `id` = NEW.parking_lot_id;
END$$

-- 当评价状态变更时更新停车场平均评分
CREATE TRIGGER `update_parking_lot_rating_after_review_update`
AFTER UPDATE ON `reviews`
FOR EACH ROW
BEGIN
    UPDATE `parking_lots` 
    SET `rating` = (
        SELECT COALESCE(ROUND(AVG(rating), 2), 0.00)
        FROM `reviews` 
        WHERE `parking_lot_id` = NEW.parking_lot_id 
        AND `status` = 'visible'
    )
    WHERE `id` = NEW.parking_lot_id;
END$$

-- 当评价被删除时更新停车场平均评分
CREATE TRIGGER `update_parking_lot_rating_after_review_delete`
AFTER DELETE ON `reviews`
FOR EACH ROW
BEGIN
    UPDATE `parking_lots` 
    SET `rating` = (
        SELECT COALESCE(ROUND(AVG(rating), 2), 0.00)
        FROM `reviews` 
        WHERE `parking_lot_id` = OLD.parking_lot_id 
        AND `status` = 'visible'
    )
    WHERE `id` = OLD.parking_lot_id;
END$$

DELIMITER;

-- 创建月销量更新的存储过程（可以通过定时任务调用）
DELIMITER $$

CREATE PROCEDURE `UpdateMonthlySales`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE lot_id BIGINT UNSIGNED;
    DECLARE sales_count INT;
    
    -- 声明游标
    DECLARE cur CURSOR FOR 
        SELECT p.id, 
               COALESCE(COUNT(o.id), 0) as monthly_orders
        FROM parking_lots p
        LEFT JOIN orders o ON p.id = o.parking_lot_id 
            AND o.created_at >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
            AND o.status IN ('completed', 'in_progress')
        GROUP BY p.id;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 打开游标
    OPEN cur;
    
    -- 循环更新
    read_loop: LOOP
        FETCH cur INTO lot_id, sales_count;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        UPDATE parking_lots 
        SET monthly_sales = sales_count 
        WHERE id = lot_id;
    END LOOP;
    
    -- 关闭游标
    CLOSE cur;
END$$

DELIMITER;

-- 初始化现有停车场的评分（基于现有评价计算）
UPDATE `parking_lots` p
SET
    `rating` = (
        SELECT COALESCE(ROUND(AVG(r.rating), 2), 0.00)
        FROM `reviews` r
        WHERE
            r.parking_lot_id = p.id
            AND r.status = 'visible'
    );

-- 调用存储过程初始化月销量
CALL UpdateMonthlySales ();