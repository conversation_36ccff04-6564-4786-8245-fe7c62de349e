# 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock
bun.lockb
.kiro
# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov/
coverage/
*.lcov
.nyc_output/

# 缓存文件
.cache/
.parcel-cache/
.npm/
.eslintcache
.stylelintcache

# 构建输出
dist/
build/
out/
.next/
.nuxt/
.vuepress/dist/

# 服务器相关
uploads/
temp/
tmp/
*.tmp

# 数据库文件
*.sqlite
*.sqlite3
*.db

# 证书文件
certs/
*.pem
*.key
*.crt
*.p12
*.pfx

# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 微信小程序
unpackage/
.hbuilderx/

# Vue.js
.vuepress/dist

# 测试文件
test-results/
playwright-report/
coverage/

# PM2
.pm2/
ecosystem.config.js

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar

# 临时文件
*.tmp
*.temp
